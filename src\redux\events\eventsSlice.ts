import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../components/api/api";

interface IEventsState {
  events: ITheft[];
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

const initialState: IEventsState = {
  events: [
    {
      camera: 1,
      timestamp: "22:05:52",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        email: "<EMAIL>",
        phone: "************",
        fraud_risk: 3,
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        status: "Good",
        alerts: 12,
        revenue: 1560,
      },
      employee: null,
      confidence: 0.75,
      video_clip: "",
    },
  ],
  status: "idle",
  error: null,
};

// Create the async thunk
export const fetchEvents = createAsyncThunk(
  "events/fetchEvents",
  async (filters: IEventsFilters | undefined, { rejectWithValue }) => {
    try {
      const response = await api.get("api/events", { params: filters });
      return response.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const eventsSlice = createSlice({
  name: "events",
  initialState,
  reducers: {
    setEvents: (state, action) => {
      state.events = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchEvents.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchEvents.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.events = action.payload;
      })
      .addCase(fetchEvents.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload as string;
      });
  },
});

export const { setEvents } = eventsSlice.actions;
export default eventsSlice.reducer;
