import { createSlice } from "@reduxjs/toolkit";

interface IEmployeeState {
  employees: IEmployee[];
}

const initialState: IEmployeeState = {
  employees: [
    {
      id: 1,
      employee_id: "#123456",
      first_name: "<PERSON><PERSON><PERSON>",
      last_name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 2,
      employee_id: "#123456",
      first_name: "<PERSON><PERSON><PERSON>",
      last_name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 3,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 4,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 5,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 6,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 6,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 6,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 6,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 6,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 6,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
    {
      id: 6,
      employee_id: "#123456",
      first_name: "Giorgi",
      last_name: "Dzotsenidze",
      email: "<EMAIL>",
      phone: "************",
      risk_score: 3,
      department: "sales",
      role: "Assistant",
      last_alert: "2025-6-15",
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },
      image: "/images/employee-image.png",
    },
  ],
};

const employeesSlice = createSlice({
  name: "employees",
  initialState,
  reducers: {
    setEmployees: (state, action) => {
      state.employees = action.payload;
    },
  },
});

export const { setEmployees } = employeesSlice.actions;
export default employeesSlice.reducer;
