import React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "../../redux/store";
import OverViewItem from "./OverViewItem";

const Overview: React.FC = () => {
  const { dashboard } = useSelector((state: RootState) => state.dashboard);
  const overviewData: IDashboardOverview[] = dashboard.overview;

  return (
    <div className="flex rounded-[1.2rem]">
      {overviewData.map((item) => (
        <OverViewItem key={item.id} {...item} />
      ))}
    </div>
  );
};

export default Overview;
