import React, { useMemo } from "react";
import Header from "../components/Header";
import { useSelector } from "react-redux";
import type { RootState } from "../redux/store";
import BranchCard from "../components/ForBranches/BranchCard";
import LeafletMap from "../components/ForBranches/Map";

const Branches: React.FC = () => {
  const { branches } = useSelector((state: RootState) => state.branches);

  const locations = useMemo(
    () =>
      branches?.map((branch, index) => {
        return {
          id: index.toString(),
          position: [
            Number(branch.coordinates.latitude),
            Number(branch.coordinates.longitude),
          ] as [number, number],
          name: branch.name,
          status: branch.status,
        };
      }),
    [branches]
  );

  return (
    <section className="flex gap-[2rem]">
      <div className="flex-1">
        <Header />
        <div className="flex flex-wrap gap-[2rem] max-h-[calc(100vh-10rem)] overflow-y-auto ">
          {branches.map((branch, index) => (
            <div key={index} className="min-w-[27.6rem] flex-1">
              <BranchCard branch={branch} />
            </div>
          ))}
        </div>
      </div>
      <div className="static flex-1 overflow-hidden">
        <LeafletMap locations={locations} />
      </div>
    </section>
  );
};

export default Branches;
