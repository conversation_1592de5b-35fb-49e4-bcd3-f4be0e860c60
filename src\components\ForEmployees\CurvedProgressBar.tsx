import React from "react";

// --- Configuration Constants ---
// Adjust these to fine-tune the overall size and curvature of the gauge.
const GAUGE_VISUAL_WIDTH = 95; // Total horizontal span of the gauge (including stroke)
const GAUGE_VISUAL_HEIGHT = 45; // Total vertical span of the gauge (including stroke)
const STROKE_WIDTH = 13; // Thickness of the arc line

// Colors
const FILL_COLOR = "#a31515"; // Pure red for the filled part
const EMPTY_COLOR = "#E6E6E6"; // Light gray for the empty track

const PADDING = STROKE_WIDTH / 2; // Half stroke width for padding around the path

// --- Define Points and Control Points for the Custom Path ---

// This controls how much the "foot" section curves inwards and upwards from the base.
// Adjust these to get the desired "inward curve from flat base" look.
const FOOT_CURVE_HEIGHT = STROKE_WIDTH * 0; // How high the initial foot curve segment goes
const FOOT_CURVE_INSET_X = STROKE_WIDTH * 0.0; // How far inwards the curve starts from the absolute edge

// Calculate the absolute coordinates for the overall visual gauge boundaries
const startX_base = PADDING; // Absolute left edge of the gauge
const endX_base = GAUGE_VISUAL_WIDTH - PADDING; // Absolute right edge of the gauge
const baseY = GAUGE_VISUAL_HEIGHT - PADDING; // Y-coordinate for the absolute bottom of the gauge

// Define the start/end points for the MAIN elliptical arc.
// These points are *after* the initial Bezier curve for the foot.
const arcStartX = startX_base + FOOT_CURVE_INSET_X; // X where left foot connects to main arc
const arcStartY = baseY - FOOT_CURVE_HEIGHT; // Y where left foot connects to main arc
const arcEndX = endX_base - FOOT_CURVE_INSET_X; // X where right foot connects to main arc
const arcEndY = arcStartY; // Y where right foot connects to main arc (same level)

// Define the radii for the MAIN elliptical arc.
// This arc spans from (arcStartX, arcStartY) to (arcEndX, arcEndY)
const MAIN_ARC_RX = (arcEndX - arcStartX) / 2; // Half the horizontal span of the main arc
const MAIN_ARC_RY = GAUGE_VISUAL_HEIGHT - PADDING - FOOT_CURVE_HEIGHT; // Vertical height of the main arc segment

// Define the Control Points for the Quadratic Bezier (Q) curves for the "feet".
// A Q curve goes from (StartPoint) -> (ControlPoint) -> (EndPoint).
// By placing the control point strategically, we dictate the curve's initial tangent.

// Left foot Q curve: (startX_base, baseY) -> (Q_LCX, Q_LCY) -> (arcStartX, arcStartY)
// Q_LCX: Start at startX_base to keep the very first point on the absolute edge.
// Q_LCY: Pull this control point slightly *above* the baseY to make the curve immediately go upwards.
const Q_LCX = startX_base;
const Q_LCY = baseY - FOOT_CURVE_HEIGHT * 0.2; // Small vertical pull to start curving up immediately

// Right foot Q curve: (arcEndX, arcEndY) -> (Q_RCX, Q_RCY) -> (endX_base, baseY)
// Q_RCX: End at endX_base.
// Q_RCY: Pull this control point slightly *above* the baseY to make the curve immediately go upwards.
const Q_RCX = endX_base;
const Q_RCY = baseY - FOOT_CURVE_HEIGHT * 0.2; // Small vertical pull to start curving up immediately

// Define the full SVG Path 'd' attribute.
// M: MoveTo - starts the path at the absolute bottom-left corner of the gauge's visual area.
// Q: Quadratic Bezier Curve - creates the left "foot" curve.
// A: Elliptical Arc - creates the main body of the gauge.
// Q: Quadratic Bezier Curve - creates the right "foot" curve.
const PATH_D = `
  M ${startX_base} ${baseY}
  Q ${Q_LCX} ${Q_LCY} ${arcStartX} ${arcStartY}
  A ${MAIN_ARC_RX} ${MAIN_ARC_RY} 0 0 1 ${arcEndX} ${arcEndY}
  Q ${Q_RCX} ${Q_RCY} ${endX_base} ${baseY}
`;

function CurvedProgressBar({ score = 75 }) {
  const normalizedScore = Math.max(0, Math.min(100, score));

  // useRef and useEffect are used to dynamically get the exact length of the SVG path.
  // This is essential for accurate progress bar filling, especially for complex paths like this.
  const pathRef = React.useRef<SVGPathElement | null>(null);
  const [actualPathLength, setActualPathLength] = React.useState(0);

  React.useEffect(() => {
    if (pathRef.current) {
      const length = pathRef.current.getTotalLength();
      if (length > 0 && length !== actualPathLength) {
        setActualPathLength(length);
      }
    }
  }, [PATH_D, actualPathLength]); // Dependency on PATH_D ensures recalculation if constants change

  // Calculate the `strokeDashoffset` to control the filled portion of the path.
  // When score is 0%, offset is `actualPathLength` (hides the fill).
  // When score is 100%, offset is 0 (shows full fill).
  const currentDashOffset = (
    actualPathLength -
    (normalizedScore / 100) * actualPathLength
  ).toFixed(3);

  return (
    <div className="bg-white pt-[2rem] !pb-0">
      {/* Container for the SVG and text, positioned relative for absolute text overlay */}
      <div
        className="relative w-full flex items-center justify-center"
        style={{ height: `${GAUGE_VISUAL_HEIGHT + 85}px` }}
      >
        {" "}
        {/* Add extra space for text below gauge */}
        <svg
          width="100%"
          height="100%"
          // viewBox defines the internal coordinate system of the SVG.
          // It's set to encompass the entire visual gauge including stroke.
          viewBox={`0 0 ${GAUGE_VISUAL_WIDTH} ${GAUGE_VISUAL_HEIGHT}`}
          className="transform -translate-y-1" // Adjust vertical position within its container
        >
          {/* Background Path (the light gray, unfilled track) */}
          <path
            d={PATH_D} // The custom path definition
            fill="transparent"
            stroke={EMPTY_COLOR}
            strokeWidth={STROKE_WIDTH}
            strokeLinecap="butt" // Crucial: No automatic end-capping; the rounding is in PATH_D
            strokeLinejoin="round" // Ensures smooth joins between path segments
            strokeDasharray={actualPathLength} // Full length to show the entire background path
          />

          {/* Filled Path (the red, dynamic progress) */}
          <path
            ref={pathRef} // Attach ref to this path to measure its length
            d={PATH_D} // The same custom path definition
            fill="transparent"
            stroke={FILL_COLOR}
            strokeWidth={STROKE_WIDTH}
            strokeLinecap="butt"
            strokeLinejoin="round"
            // `strokeDasharray` creates a pattern of dashes and gaps.
            // By setting both to `actualPathLength`, we create one long dash and one long gap.
            strokeDasharray={`${actualPathLength} ${actualPathLength}`}
            // `strokeDashoffset` shifts the starting point of the dash pattern.
            // This is how the progress is animated.
            strokeDashoffset={currentDashOffset}
            style={{ transition: "stroke-dashoffset 0.5s ease-out" }} // Smooth animation for score changes
          />
        </svg>
        {/* Text Overlay for Score */}
        <div className="absolute  left-1/2 -translate-x-1/2">
          <p className="text-[2rem] font-bold text-gray-900">
            {normalizedScore}%
          </p>
          <p className="text-[#525252] text-[1.1rem]">Total Score</p>
        </div>
      </div>
    </div>
  );
}

export default CurvedProgressBar;
