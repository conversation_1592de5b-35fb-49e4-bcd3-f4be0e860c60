import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "../../redux/store";
import { fetchEvents } from "../../redux/events/eventsSlice";

import TheftCard from "../Shared/TheftCard";

const TheftAlerts: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { events, status } = useSelector((state: RootState) => state.events);
  const { theftAlerts } = dashboard;
  const { inHouse, customer } = theftAlerts;

  useEffect(() => {
    dispatch(fetchEvents({ organization: "1", branch: "1" }));
  }, [dispatch]);
  return (
    <section className="mt-[2rem] pb-[1.8rem]">
      {children}
      <div className="flex flex-wrap gap-[2rem] mt-[1.5rem]">
        <div className="flex-1 flex flex-col gap-[1.3rem]">
          <h3 className="text-[1.4rem] font-medium text-[#000] ml-[1.6rem]">
            Inhouse
          </h3>
          <div className="flex flex-col gap-[1rem]">
            {inHouse.map((alert: ITheftAlertsData, index: number) => (
              <TheftCard key={index} {...alert} />
            ))}
          </div>
        </div>

        <div className="flex-1 flex flex-col gap-[1.3rem]">
          <h3 className="text-[1.4rem] font-medium text-[#000] ml-[1.6rem]">
            Customer
          </h3>
          <div className="flex flex-col gap-[1rem]">
            {customer.map((alert: ITheftAlertsData, index: number) => (
              <TheftCard key={index} {...alert} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TheftAlerts;
