import React, { useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import Sidebar from "../components/Sidebar";
import Header from "../components/Header";
import { useDispatch } from "react-redux";
import { setActivePath } from "../redux/misc/miscSlice";
import { findRouteByPath } from "../routes/routeConfig";

const MainLayout: React.FC = () => {
  const location = useLocation();
  const dispatch = useDispatch();

  useEffect(() => {
    const currentRoute = findRouteByPath(location.pathname);

    if (currentRoute) {
      dispatch(setActivePath(currentRoute.navPath));
    }
  }, [location.pathname, dispatch]);

  // Get current route for conditional rendering
  const currentRoute = findRouteByPath(location.pathname);

  return (
    <div className="flex h-screen overflow-hidden">
      <Sidebar />
      <div className="flex flex-col flex-1">
        {currentRoute?.showHeader && <Header />}
        <main className="flex-1 overflow-auto pl-[1rem] pr-[2.2rem]">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
