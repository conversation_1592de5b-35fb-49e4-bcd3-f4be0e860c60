// Define a route configuration that includes metadata
export const routes = [
  {
    path: "/dashboard",
    navPath: "",
    title: "Dashboard",
    showHeader: true,
  },
  {
    path: "/dashboard/branches",
    navPath: "branches",
    title: "Branches",
    showHeader: false,
  },
  {
    path: "/dashboard/employees",
    navPath: "employees",
    title: "Employees",
    showHeader: true,
  },
  {
    path: "/dashboard/employees/:id",
    navPath: "employees", // This links it to the employees section
    title: "Employee Details",
    showHeader: true,
    isDetail: true,
  },
  {
    path: "/dashboard/alerts",
    navPath: "alerts",
    title: "Alerts",
    showHeader: true,
  },
  {
    path: "/dashboard/alerts/thefts",
    navPath: "thefts",
    title: "Thefts",
    showHeader: true,
  },
];

// Helper function to find route by path
export const findRouteByPath = (pathname: string) => {
  return routes.find((route) => {
    // Convert route path to regex pattern to match dynamic routes
    const pattern = route.path.replace(/:\w+/g, "[^/]+");
    const regex = new RegExp(`^${pattern}$`);
    return regex.test(pathname);
  });
};
