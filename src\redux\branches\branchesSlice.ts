import { createSlice } from "@reduxjs/toolkit";

interface IAuthState {
  branches: IBranch[];
}

const initialState: IAuthState = {
  branches: [
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 3,
      coordinates: { longitude: "44.787197", latitude: "41.715137" },
      status: "Good",
      alerts: 12,
      revenue: 1560,
    },
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 95,
      coordinates: { longitude: "44.797197", latitude: "41.735137" },
      status: "Critical",
      alerts: 12,
      revenue: 1560,
    },
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 77,
      coordinates: { longitude: "44.7687197", latitude: "41.715137" },
      status: "Needs attention",
      alerts: 12,
      revenue: 1560,
    },
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 3,
      coordinates: { longitude: "44.723545", latitude: "41.715137" },
      status: "Good",
      alerts: 12,
      revenue: 1560,
    },
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 3,
      coordinates: { longitude: "44.775197", latitude: "41.716537" },
      status: "Good",
      alerts: 12,
      revenue: 1560,
    },
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 3,
      coordinates: { longitude: "44.787197", latitude: "41.715137" },
      status: "Good",
      alerts: 12,
      revenue: 1560,
    },
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 3,
      coordinates: { longitude: "44.747197", latitude: "41.715137" },
      status: "Good",
      alerts: 12,
      revenue: 1560,
    },
    {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 3,
      coordinates: { longitude: "44.796197", latitude: "41.715137" },
      status: "Good",
      alerts: 12,
      revenue: 1560,
    },
  ],
};

const branchesSlice = createSlice({
  name: "misc",
  initialState,
  reducers: {
    setBranches: (state, action) => {
      state.branches = action.payload;
    },
  },
});

export const { setBranches } = branchesSlice.actions;
export default branchesSlice.reducer;
