import React from "react";
interface IPaginationProps {
  pagination: {
    links: {
      next: string | null;
      previous: string | null;
    };
    count: number;
    total_pages: number;
    current_page: number;
  };
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
}

const Pagination: React.FC<IPaginationProps> = ({ pagination }) => {
  return (
    <div className="w-full flex items-center gap-[3.2rem] justify-end mt-[1.2rem]">
      <div className="flex items-center gap-[1.2rem]">
        <span className="text-[1.4rem] text-[$09090B] font-[500] leading-[2rem]">
          Rows Per Page
        </span>
        <input
          type="number"
          defaultValue={10}
          className="outline-none max-w-[7rem] max-h-[3.2rem] px-[1.2rem] py-[.8rem] border-[1px] border-[#E4E4E7] bg-[#fff] rounded-[.6rem] text-[1.4rem] leading-[2rem]"
        />
      </div>
      <span className="text-[1.4rem] text-[$09090B] font-[500] leading-[2rem]">
        Page {pagination.current_page} of {pagination.total_pages}
      </span>
      <div className="flex items-center gap-[.8rem]">
        <button
          className={`cursor-pointer p-[.8rem] border-[1px] border-[#E4E4E7] rounded-[.8rem] bg-[#fff]  ${
            pagination.links.previous ? "" : "opacity-[.5]"
          }`}
        >
          <img src="/images/chevrons-left.png" alt="" />
        </button>
        <button
          className={`cursor-pointer p-[.8rem] border-[1px] border-[#E4E4E7] rounded-[.8rem] bg-[#fff]  ${
            pagination.links.previous ? "" : "opacity-[.5]"
          }`}
        >
          <img src="/images/chevron-left.png" alt="" />
        </button>
        <button
          className={`cursor-pointer p-[.8rem] border-[1px] border-[#E4E4E7] rounded-[.8rem] bg-[#fff]  ${
            pagination.links.next ? "" : "opacity-[.5]"
          }`}
        >
          <img src="/images/chevron-right.png" alt="" />
        </button>
        <button
          className={`cursor-pointer p-[.8rem] border-[1px] border-[#E4E4E7] rounded-[.8rem] bg-[#fff]  ${
            pagination.links.next !== null ? "" : "opacity-[.5]"
          }`}
        >
          <img src="/images/chevrons-right.png" alt="" />
        </button>
      </div>
    </div>
  );
};
export default Pagination;
