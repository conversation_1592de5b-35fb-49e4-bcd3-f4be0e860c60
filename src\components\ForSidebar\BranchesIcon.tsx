import React from "react";

interface IIconProps {
  active: boolean;
}

const BranchesIcon: React.FC<IIconProps> = ({ active }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={`${active ? "#4C9DF9" : "none"}`}
    >
      <path
        d="M6.15543 0.833328C6.69673 0.833323 7.13445 0.833319 7.48914 0.863371C7.85504 0.894374 8.17719 0.959994 8.47491 1.11731C8.94668 1.36659 9.3269 1.76252 9.56411 2.2453C9.7127 2.54774 9.77497 2.87495 9.80454 3.25033C9.83334 3.61589 9.83334 4.06789 9.83333 4.63104V5.03047H11.25C12.0065 5.03047 12.6696 5.1771 13.1225 5.66271C13.5664 6.13869 13.7 6.82725 13.7 7.62904V14.1667H14.5C14.7761 14.1667 15 14.3905 15 14.6667C15 14.9428 14.7761 15.1667 14.5 15.1667H1.5C1.22386 15.1667 1 14.9428 1 14.6667C1 14.3905 1.22386 14.1667 1.5 14.1667H2.16667L2.16667 4.63104C2.16666 4.06789 2.16666 3.61589 2.19546 3.25033C2.22503 2.87495 2.2873 2.54774 2.43589 2.2453C2.6731 1.76252 3.05332 1.36659 3.52509 1.11731C3.82282 0.959994 4.14496 0.894374 4.51086 0.863371C4.86555 0.833319 5.30327 0.833323 5.84457 0.833328H6.15543ZM5.33333 3.49999C5.05719 3.49999 4.83333 3.72385 4.83333 4C4.83333 4.27614 5.05719 4.5 5.33333 4.5H6.66667C6.94281 4.5 7.16667 4.27614 7.16667 4C7.16667 3.72385 6.94281 3.49999 6.66667 3.49999H5.33333ZM4.83333 6.66666C4.83333 6.9428 5.05719 7.16666 5.33333 7.16666H6.66667C6.94281 7.16666 7.16667 6.9428 7.16667 6.66666C7.16667 6.39052 6.94281 6.16666 6.66667 6.16666H5.33333C5.05719 6.16666 4.83333 6.39052 4.83333 6.66666ZM5.33333 8.83333C5.05719 8.83333 4.83333 9.05719 4.83333 9.33333C4.83333 9.60947 5.05719 9.83333 5.33333 9.83333H6.66667C6.94281 9.83333 7.16667 9.60947 7.16667 9.33333C7.16667 9.05719 6.94281 8.83333 6.66667 8.83333H5.33333Z"
        stroke={active ? "#fff" : `#55555E`}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default BranchesIcon;
