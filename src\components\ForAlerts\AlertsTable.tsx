import React, { useState } from "react";

interface IAlertsTableProps {
  showUser: boolean;
  alerts: IAlert[];
}

const AlertsTable: React.FC<IAlertsTableProps> = ({ alerts, showUser }) => {
  const [showMessageId, setShowMessageId] = useState<number>(0);

  // Calculate the number of columns in the table
  const columnCount = showUser ? 6 : 5; // Adjust based on your actual column count

  return (
    <div className="bg-[#fff] w-full rounded-[1.6rem] border-[1px] border-[#E8E8E8] pb-[1rem]">
      <div className="flex items-center gap-[.8rem] p-[1.6rem]">
        <img src="/images/employee-table-img.png" alt="" />
        <span className="text-[1.4rem] font-[500] leading-[2.8rem] text-[#282828]">
          Employees
        </span>
      </div>
      <div className="">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="!bg-[#fff] border-t-[1px] border-t-[#E8E8E8]">
            <tr className="">
              <th
                scope="col"
                className=" sticky top-0 backdrop-blur-sm backdrop-filter  py-3.5 pr-3 pl-[1.6rem] text-left text-[1.2rem] font-[500] text-[#282828] "
              >
                Time
              </th>
              {showUser && (
                <th
                  scope="col"
                  className="hidden sticky top-0 backdrop-blur-sm backdrop-filter px-3 py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] lg:table-cell"
                >
                  Employee
                </th>
              )}

              <th
                scope="col"
                className="hidden sticky top-0 backdrop-blur-sm backdrop-filter px-3 py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Branch
              </th>
              <th
                scope="col"
                className="hidden sticky top-0 backdrop-blur-sm backdrop-filter px-3 py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Status
              </th>
              <th
                scope="col"
                className="px-3 sticky top-0 backdrop-blur-sm backdrop-filter py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Type
              </th>

              <th scope="col" className="relative py-3.5 pr-4 pl-3 sm:pr-0">
                <span className="sr-only">Edit</span>
              </th>
            </tr>
          </thead>
          <tbody className=" divide-y divide-gray-200 bg-white">
            {alerts.map((alert, index) => (
              <React.Fragment key={index}>
                <tr className="">
                  <td className="hidden !pr-[0] pl-[1.6rem] py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                    {alert.time}
                  </td>

                  {showUser && (
                    <td className="w-full max-w-0 py-4 pr-3 text-[1.2rem] pl-[.75rem] font-[500] text-[#000] sm:w-auto sm:max-w-none ">
                      {`${alert.employee.first_name} ${alert.employee.last_name}`}
                      <dl className="font-normal lg:hidden">
                        <dt className="sr-only">Title</dt>
                        <dd className="mt-1 truncate text-gray-700">
                          Front-end Developer
                        </dd>
                        <dt className="sr-only sm:hidden">Email</dt>
                        <dd className="mt-1 truncate text-gray-500 sm:hidden">
                          <EMAIL>
                        </dd>
                      </dl>
                    </td>
                  )}

                  <td className="hidden px-3 py-4 text-[1.2rem] font-[500] text-[#000] lg:table-cell">
                    {alert.employee.branch.name}
                  </td>
                  <td className="hidden px-3 py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                    <span>{alert.status}</span>
                  </td>
                  <td className="px-3 py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                    <span>{alert.type}</span>
                  </td>

                  <td className="py-4 pr-4 pl-3 text-right text-sm font-medium sm:pr-0">
                    <img
                      className="cursor-pointer"
                      src={` ${
                        showMessageId === alert.id
                          ? "/images/arrow-up.png"
                          : "/images/alert-arrow-down.png"
                      } `}
                      alt=""
                      onClick={() => {
                        if (showMessageId === alert.id) {
                          setShowMessageId(0);
                        } else {
                          setShowMessageId(alert.id);
                        }
                      }}
                    />
                  </td>
                </tr>
                {showMessageId === alert.id && (
                  <tr>
                    <td colSpan={columnCount} className="p-0 border-0">
                      <div className="w-full flex flex-col bg-[#F7F7F7] border-b-[1px] border-b-[#D9D9D9] px-[1.6rem] py-[1.2rem]">
                        <span className="text-[1.1rem] text-[#717171] font-[500]">
                          Message
                        </span>
                        <span className="text-[1.2rem] text-[#000] font-[500]">
                          {alert.message}
                        </span>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AlertsTable;
