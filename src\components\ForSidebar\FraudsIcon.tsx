import React from "react";

interface IIconProps {
  active: boolean;
}

const FraudsIcon: React.FC<IIconProps> = ({ active }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={`${active ? "#4C9DF9" : "none"}`}
    >
      <path
        d="M4.33301 9.33301C4.68963 9.33292 5.04128 9.41501 5.36035 9.57129L5.49512 9.64258C5.84859 9.84534 6.14296 10.1375 6.34863 10.4893L6.58984 10.9014L7.0127 10.6797L6.8916 10.7441L6.57227 10.9141L6.63281 11.2705C6.71408 11.742 6.64895 12.2272 6.44531 12.6602C6.26714 13.0389 5.9907 13.3615 5.64648 13.5957L5.49512 13.6904C5.13197 13.8987 4.72016 14.0054 4.30371 14L4.125 13.9912C3.70802 13.9537 3.30984 13.8046 2.97168 13.5615L2.83105 13.4512C2.4651 13.143 2.20232 12.7299 2.0791 12.2676C1.95596 11.8054 1.97772 11.3165 2.1416 10.8672C2.28509 10.474 2.53164 10.1275 2.85352 9.86328L2.99609 9.75488C3.33898 9.51497 3.73922 9.37286 4.1543 9.34082L4.33301 9.33301ZM10.9258 9.45312C11.3951 9.29607 11.9007 9.29253 12.3691 9.44043L12.5674 9.5127C13.0891 9.73101 13.5141 10.1317 13.7627 10.6396C13.9802 11.0841 14.0496 11.5845 13.9648 12.0684L13.9189 12.2744C13.7897 12.7523 13.5127 13.1752 13.1299 13.4834L12.96 13.6074C12.4893 13.9208 11.9209 14.0527 11.3604 13.9785C10.87 13.9135 10.4144 13.6949 10.0586 13.3564L9.91211 13.2041C9.53964 12.7792 9.33358 12.233 9.33301 11.668C9.33339 11.5309 9.34533 11.3982 9.36719 11.2695L9.42773 10.9131L9.1084 10.7441L8.9873 10.6797L9.41016 10.9014L9.65137 10.4893C9.90096 10.0619 10.2795 9.72667 10.7295 9.5293L10.9258 9.45312ZM3.30371 6.94141L3.39453 6.55664L3.74902 5.05273C3.88284 4.48603 3.99298 4.02446 4.10547 3.64551L4.21973 3.29297V3.29199C4.37768 2.85212 4.55904 2.54782 4.83008 2.32324C4.92043 2.24859 4.99983 2.21302 5.0752 2.20703L5.07617 2.20801C5.11382 2.20535 5.15123 2.20932 5.18848 2.21973L5.30176 2.26758H5.30371C5.36545 2.30027 5.43783 2.34995 5.51758 2.4043V2.40332L5.55762 2.43066L5.55957 2.43262C5.60847 2.46569 5.66029 2.4994 5.71582 2.5332L5.89648 2.63379L5.89746 2.63477C6.23965 2.80609 6.60902 2.8165 6.95898 2.75293C7.21876 2.70574 7.48613 2.61529 7.75391 2.49902L8.02148 2.375C8.27383 2.25153 8.61613 2.14928 8.98242 2.08301L9.35352 2.0293C9.79126 1.98198 10.2328 1.98871 10.5732 2.07227L10.7119 2.11328H10.7129C10.9573 2.19669 11.1105 2.28853 11.2207 2.4043L11.3184 2.5293L11.3193 2.53027C11.3766 2.61732 11.422 2.71693 11.4707 2.83496V2.83594L11.5303 2.98535V2.98438C11.5615 3.06416 11.5951 3.14859 11.6396 3.25195V3.25293C11.9607 3.99909 12.182 4.78817 12.377 5.58496L12.5664 6.38379L12.6055 6.55371L12.6943 6.94141H3.30371Z"
        stroke="#55555E"
      />
    </svg>
  );
};

export default FraudsIcon;
