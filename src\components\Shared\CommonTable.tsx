interface CommonTableProps {
  headers: IHighRiskEmployeesHeader[] | IFraudAlertsHeader[];
  data: IHighRiskEmployeesData[] | IFraudAlertsData[];
  redirect: boolean;
  className?: string;
  redirectFunc?: (id: number) => void;
}

export default function CommonTable({
  headers,
  data,
  redirect,
  className = "",
  redirectFunc,
}: CommonTableProps) {
  return (
    <div className={`overflow-x-auto ${className}`}>
      <table className="w-full border-collapse">
        <thead>
          <tr>
            {headers.map((header) => (
              <th
                key={header.key}
                className={`text-[1.2rem] px-[1.6rem] h-[2.4rem] font-medium text-[#282828] border-b border-[#E8E8E8]  ${
                  header?.align === "center"
                    ? "text-center"
                    : header?.align === "right"
                    ? "text-right"
                    : "text-left"
                }`}
              >
                {header.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr
              key={index}
              className={`hover:bg-gray-50 px-[1.6rem] ${
                index !== data.length - 1 ? "border-b border-[#E8E8E8]" : ""
              }`}
            >
              {headers.map((header) => (
                <td
                  key={`${index}-${header.key}`}
                  className={`px-[1.6rem] text-[1.2rem] h-[3.6rem] text-[#000000] ${
                    header?.align === "center"
                      ? "text-center"
                      : header?.align === "right"
                      ? "text-right"
                      : "text-left"
                  }`}
                >
                  {/* Handle risk score styling */}
                  {header?.key === "riskScore"
                    ? (() => {
                        const score =
                          row[
                            header.key as keyof (
                              | IHighRiskEmployeesData
                              | IFraudAlertsData
                            )
                          ];
                        const numericScore =
                          typeof score === "number" ? score : 0;
                        return (
                          <div className="w-full flex justify-center items-center">
                            <span
                              className={`w-[2rem] h-[2rem] flex justify-center items-center rounded-full text-[1.2rem] font-medium  ${
                                numericScore >= 90
                                  ? "bg-[#FBE0E0] text-[#7C1111]"
                                  : numericScore >= 80
                                  ? "bg-[#FAF4D2] text-[#534706]"
                                  : "bg-[#E0FBEC] text-[#117C43]"
                              }`}
                            >
                              {score}
                            </span>
                          </div>
                        );
                      })()
                    : header?.key === "type"
                    ? (() => {
                        const type = (row as IFraudAlertsData).type;
                        return (
                          <div className="w-full flex justify-end items-center gap-[0.8rem]">
                            <span
                              className={`w-fit  px-[0.8rem] flex justify-center items-center rounded-full text-[1.2rem] font-medium  ${
                                type === "Needs Attention"
                                  ? "bg-[#FAF4D2] text-[#534706]"
                                  : type === "Critical"
                                  ? "bg-[#FBE0E0] text-[#7C1111]"
                                  : "bg-[#E0FBEC] text-[#117C43]"
                              }`}
                            >
                              {type}
                            </span>
                            {redirect ? (
                              <img
                                src="/images/dashboard/redirect.svg"
                                alt="Redirect"
                                className="cursor-pointer"
                                onClick={() => redirectFunc?.(row.id as number)}
                              />
                            ) : null}
                          </div>
                        );
                      })()
                    : row[
                        header?.key as keyof (
                          | IHighRiskEmployeesData
                          | IFraudAlertsData
                        )
                      ]}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      {data.length === 0 && (
        <div className="text-center py-8 text-gray-500">No data available</div>
      )}
    </div>
  );
}
