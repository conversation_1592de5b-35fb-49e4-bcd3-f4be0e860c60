import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";

interface IMapProps {
  locations: {
    id: string;
    position: [number, number];
    name: string;
    status: string;
  }[];
}

// --- IMPORTANT MARKER ICON FIX ---
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png",
  iconUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",
  shadowUrl: "https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png",
});

const redIcon = new L.Icon({
  iconUrl: "/images/red-icon.png",
  iconSize: [64, 64],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
});
const greenIcon = new L.Icon({
  iconUrl: "/images/green-icon.png",
  iconSize: [64, 64],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
});
const yellowIcon = new L.Icon({
  iconUrl: "/images/yellow-icon.png",
  iconSize: [64, 64],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
});

// const draggableIcon = new L.Icon({
//   iconUrl:
//     "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
//   iconSize: [25, 41],
//   iconAnchor: [12, 41],
//   popupAnchor: [1, -34],
//   shadowUrl:
//     "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
//   shadowSize: [41, 41],
// });

const LeafletMap: React.FC<IMapProps> = ({ locations }) => {
  const initialPosition: [number, number] = [41.716667, 44.783333]; // Tbilisi, Georgia
  //   const [draggableMarkerPosition, setDraggableMarkerPosition] = useState<
  //     [number, number]
  //   >([41.72, 44.8]);
  //   const markerRef = useRef<L.Marker>(null);

  //   const eventHandlers = useMemo(
  //     () => ({
  //       dragend() {
  //         const marker = markerRef.current;
  //         if (marker != null) {
  //           const latlng = marker.getLatLng();
  //           setDraggableMarkerPosition([latlng.lat, latlng.lng]);
  //           console.log("Draggable Marker New Position:", latlng);
  //         }
  //       },
  //     }),
  //     []
  //   );
  console.log(locations);

  return (
    <div className="h-full w-full  mx-auto border border-none overflow-hidden">
      <MapContainer
        key="my-unique-map-key" // <-- ADD THIS LINE
        center={initialPosition}
        zoom={13}
        scrollWheelZoom={true}
        style={{ width: "100%", height: "100%" }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {locations
          ? locations?.map((location) => (
              <Marker
                key={location.id}
                position={location.position as [number, number]}
                icon={
                  location.status === "Good"
                    ? greenIcon
                    : location.status === "Critical"
                    ? redIcon
                    : yellowIcon
                }
              >
                <Popup>
                  <b>{location.name}</b>
                  <br />
                  Latitude: {location.position[0].toFixed(4)}
                  <br />
                  Longitude: {location.position[1].toFixed(4)}
                </Popup>
              </Marker>
            ))
          : null}

        {/* <Marker
          draggable={true}
          eventHandlers={eventHandlers}
          position={draggableMarkerPosition}
          ref={markerRef}
          icon={draggableIcon}
        >
          <Popup minWidth={90}>
            <span style={{ fontWeight: "bold" }}>Drag Me!</span>
            <br />
            Current Lat: {draggableMarkerPosition[0].toFixed(4)}
            <br />
            Current Lng: {draggableMarkerPosition[1].toFixed(4)}
          </Popup>
        </Marker> */}
      </MapContainer>
    </div>
  );
};

export default LeafletMap;
