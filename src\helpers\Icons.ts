import DashboardIcon from "../components/ForSidebar/DashboardIcon";
import BranchesIcon from "../components/ForSidebar/BranchesIcon";
import EmployeesIcon from "../components/ForSidebar/EmpoyeesIcon";
import AlertsIcon from "../components/ForSidebar/AlertsIcon";
import FraudsIcon from "../components/ForSidebar/FraudsIcon";
import SettingsIcon from "../components/ForSidebar/SettingsIcon";
import SupportIcon from "../components/ForSidebar/SupportIcon";
import AccountSettingsIcon from "../components/ForSidebar/AccountSettingsIcon";
import DarkModeIcon from "../components/ForSidebar/DarkModeIcon";
import type { FC } from "react";

// Define the icon map type with index signature
type IconMapType = {
  [key: string]: FC<IIconProps>;
  DashboardIcon: typeof DashboardIcon;
  BranchesIcon: typeof BranchesIcon;
  EmployeesIcon: typeof EmployeesIcon;
  AlertsIcon: typeof AlertsIcon;
  FraudsIcon: typeof FraudsIcon;
  TheftsIcon: typeof FraudsIcon;
  SettingsIcon: typeof SettingsIcon;
  SupportIcon: typeof SupportIcon;
  AccountSettingsIcon: typeof AccountSettingsIcon;
  DarkModeIcon: typeof DarkModeIcon;
};

const iconMap: IconMapType = {
  DashboardIcon,
  BranchesIcon,
  EmployeesIcon,
  AlertsIcon,
  FraudsIcon,
  TheftsIcon: FraudsIcon, // Using FraudsIcon for TheftsIcon
  SettingsIcon,
  SupportIcon,
  AccountSettingsIcon,
  DarkModeIcon,
};

export default iconMap;
