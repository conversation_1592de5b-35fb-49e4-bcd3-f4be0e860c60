import React from "react";

interface IIconProps {
  active: boolean;
}

const EmployeesIcon: React.FC<IIconProps> = ({ active }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={`${active ? "#4C9DF9" : "none"}`}
    >
      <path
        d="M11.3333 13C11.3333 11.8954 9.84095 11 8 11C6.15905 11 4.66667 11.8954 4.66667 13M14 11.0002C14 10.1801 13.1773 9.47529 12 9.16667M2 11.0002C2 10.1801 2.82273 9.47529 4 9.16667M12 6.49074C12.4092 6.12452 12.6667 5.59233 12.6667 5C12.6667 3.89543 11.7712 3 10.6667 3C10.1544 3 9.68717 3.19257 9.33333 3.50926M4 6.49074C3.59083 6.12452 3.33333 5.59233 3.33333 5C3.33333 3.89543 4.22876 3 5.33333 3C5.84557 3 6.31283 3.19257 6.66667 3.50926M8 9C6.89543 9 6 8.10457 6 7C6 5.89543 6.89543 5 8 5C9.10457 5 10 5.89543 10 7C10 8.10457 9.10457 9 8 9Z"
        stroke="#55555E"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default EmployeesIcon;
