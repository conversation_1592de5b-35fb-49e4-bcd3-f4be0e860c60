import React from "react";

const TheftCard: React.FC<ITheftAlertsData> = ({
  title,
  time,
  score,
  address,
  text,
  avatarPath,
  employee,
}: ITheftAlertsData) => {
  return (
    <div className="w-full border-1 border-solid border-[#E8E8E8] rounded-[1.6rem] bg-white overflow-hidden py-[1.4rem] px-[1.6rem]">
      <div className="flex justify-between items-center">
        <span className="text-[#000] text-[1.4rem] font-[600]">{title}</span>
        <span
          className={`px-[0.8rem] flex justify-center items-center rounded-full text-[1.2rem] font-medium  ${
            score >= 90
              ? "bg-[#FBE0E0] text-[#7C1111]"
              : score >= 80
              ? "bg-[#FAF4D2] text-[#534706]"
              : "bg-[#E0FBEC] text-[#117C43]"
          }`}
        >
          {score}%
        </span>
      </div>
      <div className="flex justify-between items-center mt-[0.8rem]">
        <span className="text-[1.2rem] font-[400]">{time}</span>
        <span className="text-[1.2rem] font-[400]">{address}</span>
      </div>
      <p className="mt-[1.2rem] p-[1rem] bg-[#F4F4F4] rounded-[0.8rem] text-[1.4rem] font-[400] text-[#212121]">
        {text}
      </p>
      <div className="flex items-center gap-[1.2rem] mt-[1.2rem]">
        {avatarPath ? (
          <img
            src={avatarPath}
            alt={employee}
            className="w-[2.4rem] h-[2.4rem] rounded-full"
          />
        ) : (
          <div className="flex items-center justify-center w-[2.4rem] h-[2.4rem] rounded-full bg-[#E1E1E1]">
            <img
              src="/images/dashboard/incognito-gray.svg"
              alt="inncognito"
              className="w-[1.6rem] h-[1.6rem]"
            />
          </div>
        )}
        <span className="text-[1.4rem] font-medium text-[#000]">
          {employee}
        </span>
      </div>
    </div>
  );
};

export default TheftCard;
