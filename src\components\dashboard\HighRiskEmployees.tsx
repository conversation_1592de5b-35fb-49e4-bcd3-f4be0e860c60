import React from "react";
import { useSelector } from "react-redux";
import type { RootState } from "../../redux/store";

import TableHeader from "../Shared/TableHeader";
import CommonTable from "../Shared/CommonTable";

const HighRiskEmployees: React.FC = () => {
  const { dashboard } = useSelector((state: RootState) => state.dashboard);
  const { highRiskEmployees } = dashboard;
  const { headers, data } = highRiskEmployees;

  return (
    <div className="w-full">
      <TableHeader
        title="High Risk Employees"
        path="/dashboard/high-risk-employees"
        icon="/images/dashboard/people.svg"
      />
      <CommonTable
        headers={headers}
        data={data}
        redirect={false}
        className="w-full"
      />
    </div>
  );
};

export default HighRiskEmployees;
