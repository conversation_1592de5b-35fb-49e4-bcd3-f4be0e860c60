interface IIconProps {
  active: boolean;
}

interface IMenuSubmenuItem {
  name: string;
  path: string;
}

interface IMenuItem {
  name: string;
  icon: string;
  path: string;
  submenu?: IMenuSubmenuItem[];
}

interface ICoordinates {
  longitude: string;
  latitude: string;
}

interface User {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  role: string;
}

interface IEmployee {
  id: number;
  employee_id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  role: string;
  department: string;
  last_alert: string;
  risk_score: number;
  image: string;
  branch: IBranch;
}

interface IBranch {
  name: string;
  address: string;
  phone: string;
  email: string;
  status: string;
  coordinates: ICoordinates;
  fraud_risk: number;
  alerts: number;
  revenue: number;
}

interface IAlert {
  id: number;
  time: string;
  employee: IEmployee;
  message: string;
  branch: IBranch;
  status: string;
  type: string;
}

interface ITheft {
  camera: number;
  timestamp: string;
  branch: IBranch;
  employee: IEmployee | null;
  confidence: number;
  video_clip: string;
}
// Dasboard

interface IDashboard {
  dashboard: {
    overview: IDashboardOverview[];
    highRiskEmployees: IHighRiskEmployees;
    fraudAlerts: IFraudAlerts;
    theftAlerts: ITheftAlerts;
  };
}

// Dashboard Overview
interface IDashboardOverview {
  id: number;
  iconPath: string;
  iconBgColor: string;
  title: string;
  percentage: number;
  trend: string;
  trendBgColor: string;
  trendTextColor: string;
  trendText: string;
}

// High Risk Employees
interface IHighRiskEmployeesHeader {
  key: string;
  label: string;
  align?: "left" | "center" | "right";
}

interface IHighRiskEmployeesData {
  id: string;
  employee: string;
  riskScore: number;
  department: string;
}

interface IHighRiskEmployees {
  headers: IHighRiskEmployeesHeader[];
  data: IHighRiskEmployeesData[];
}

// Fraud Alerts

interface IFraudAlertsHeader {
  key: string;
  label: string;
  align?: "left" | "center" | "right";
}

interface IFraudAlertsData {
  id: number;
  time: string;
  status: string;
  employee: string;
  branch: string;
  type: string;
}

interface IFraudAlerts {
  headers: IFraudAlertsHeader[];
  data: IFraudAlertsData[];
}

// Theft Alerts

interface ITheftAlerts {
  inHouse: ITheftAlertsData[];
  customer: ITheftAlertsData[];
}

interface ITheftAlertsData {
  title: string;
  time: string;
  score: number;
  address: string;
  text: string;
  avatarPath: string | null;
  employee: string;
}

// Api call filters

interface IEventsFilters {
  organization: string;
  branch: string;
}
