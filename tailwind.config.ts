// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      keyframes: {
        // Optional: for text fade-in/out if not using simple opacity transitions
        fadeOut: {
          '0%': { opacity: '1', width: 'auto' },
          '100%': { opacity: '0', width: '0px' }, // or '0', 'hidden' for more abrupt
        },
        fadeIn: {
          '0%': { opacity: '0', width: '0px' },
          '100%': { opacity: '1', width: 'auto' },
        },
      },
      animation: {
        // Optional: for text fade-in/out
        'fade-out': 'fadeOut 0.2s ease-out forwards',
        'fade-in': 'fadeIn 0.2s ease-in forwards',
      },
    },
  },
  plugins: [],
}