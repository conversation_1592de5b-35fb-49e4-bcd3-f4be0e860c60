import React from "react";

interface IIconProps {
  active: boolean;
}

const SettingsIcon: React.FC<IIconProps> = ({ active }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={`${active ? "#4C9DF9" : "none"}`}
    >
      <path
        d="M14 11.3333V8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8V11.3333"
        stroke="#55555E"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6667 9.97197C10.6667 9.63139 10.6667 9.4611 10.7058 9.32585C10.7961 9.01312 11.0321 8.77591 11.3259 8.70252C12.1742 8.4906 12.346 9.27908 12.9546 9.52389C12.9699 9.53006 12.9853 9.53638 13.0007 9.54284C13.6621 9.82066 13.999 10.5283 13.9973 11.2457L13.9968 11.4831C13.9953 12.1168 13.6579 12.7068 13.095 12.998C12.4695 13.3216 12.2609 14.166 11.3519 13.9713C11.0607 13.9089 10.8205 13.6857 10.7185 13.3826C10.6667 13.2287 10.6667 13.0277 10.6667 12.6255V9.97197Z"
        stroke="#55555E"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.33335 12.6949C5.33335 13.0354 5.33335 13.2057 5.29426 13.341C5.20388 13.6537 4.96789 13.8909 4.67414 13.9643C3.82583 14.1762 3.65397 13.3877 3.04541 13.1429C3.03168 13.1374 3.01793 13.1318 3.00417 13.126C2.3387 12.8477 2.00101 12.1389 2.00269 11.4214L2.00326 11.1788C2.00473 10.5478 2.34182 9.96023 2.90497 9.66887C3.53048 9.34525 3.73912 8.50085 4.64812 8.69555C4.93933 8.75793 5.17948 8.98112 5.28155 9.28425C5.33335 9.43809 5.33335 9.63918 5.33335 10.0413V12.6949Z"
        stroke="#55555E"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SettingsIcon;
