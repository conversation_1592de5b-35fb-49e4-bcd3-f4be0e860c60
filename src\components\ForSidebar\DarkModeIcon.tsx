import React from "react";

interface IIconProps {
  active: boolean;
}

const DarkModeIcon: React.FC<IIconProps> = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M14 8C14 8.78793 13.8448 9.56815 13.5433 10.2961C13.2417 11.0241 12.7998 11.6855 12.2426 12.2426C11.6855 12.7998 11.0241 13.2417 10.2961 13.5433C9.56815 13.8448 8.78793 14 8 14C7.21207 14 6.43185 13.8448 5.7039 13.5433C4.97595 13.2417 4.31451 12.7998 3.75736 12.2426C3.20021 11.6855 2.75825 11.0241 2.45672 10.2961C2.15519 9.56815 2 8.78793 2 8C2 6.4087 2.63214 4.88258 3.75736 3.75736C4.88258 2.63214 6.4087 2 8 2C9.5913 2 11.1174 2.63214 12.2426 3.75736C13.3679 4.88258 14 6.4087 14 8Z"
        stroke="#55555E"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.3333 13C11.122 8.87533 4.87799 8.87533 4.66666 13M9.66666 6.16667C9.66666 6.60869 9.49106 7.03262 9.1785 7.34518C8.86594 7.65774 8.44202 7.83333 7.99999 7.83333C7.55796 7.83333 7.13404 7.65774 6.82148 7.34518C6.50892 7.03262 6.33332 6.60869 6.33332 6.16667C6.33332 5.72464 6.50892 5.30072 6.82148 4.98816C7.13404 4.67559 7.55796 4.5 7.99999 4.5C8.44202 4.5 8.86594 4.67559 9.1785 4.98816C9.49106 5.30072 9.66666 5.72464 9.66666 6.16667Z"
        stroke="#55555E"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default DarkModeIcon;
