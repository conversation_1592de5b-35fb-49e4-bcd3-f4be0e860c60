import React from "react";

const OverViewItem: React.FC<IDashboardOverview> = (overviewItem) => {
  console.log(overviewItem.iconBgColor);
  return (
    <div className="flex flex-col justify-between align-start p-6 h-[19.2rem] w-[21rem] border-r-1 border-r-[#E5E5E5] leading-0">
      <div
        className="w-[4.8rem] h-[4.8rem] rounded-full flex items-center justify-center"
        style={{ backgroundColor: `${overviewItem.iconBgColor}` }}
      >
        <img
          src={overviewItem.iconPath}
          alt={overviewItem.title}
          className={`${
            overviewItem.title === "Overall Health Indicator"
              ? "w-[3.2rem] h-[3.2rem]"
              : "w-[2.4rem] h-[2.4rem]"
          }`}
        />
      </div>
      <div className="flex flex-col gap-[0.8rem]">
        <p
          className="text-[1.2rem] leading-[1.4rem] font-medium text-[#222]"
          style={{ fontFeatureSettings: "'dlig' on" }}
        >
          {overviewItem.title}
        </p>
        <div className="flex gap-[0.4rem] items-center">
          <p className="text-[2rem] font-bold text-[#1A1A1A]">
            {overviewItem.percentage}%
          </p>
          <div
            className={`px-[0.8rem] h-[1.6rem] rounded-[1.2rem] bg-[${
              overviewItem.trend === "" || overviewItem.trend === "down"
                ? "#E0FBEC"
                : "#FBE0E0"
            }] flex justify-center items-center gap-[0.4rem]`}
          >
            {overviewItem.trend === "down" ? (
              <img
                src="/images/dashboard/trend-down.svg"
                alt="arrow-up"
                className="w-[0.8rem] h-[0.5rem]"
              />
            ) : overviewItem.trend === "up" ? (
              <img
                src="/images/dashboard/trend-up.svg"
                alt="arrow-down"
                className="w-[0.8rem] h-[0.5rem]"
              />
            ) : null}
            <span
              className={`text-[1.2rem] leading-0 font-[400] text-[${
                overviewItem.trend === "" || overviewItem.trend === "down"
                  ? "#117C43"
                  : "#7C1111"
              }]`}
            >
              {overviewItem.trendText}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OverViewItem;
