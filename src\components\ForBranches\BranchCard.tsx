import React from "react";

interface IBranchCardProps {
  branch: IBranch;
}

const BranchCard: React.FC<IBranchCardProps> = ({ branch }) => {
  return (
    <div className="p-[1.6rem] bg-[#fff] rounded-[1.6rem] flex flex-col gap-[.8rem] border-[1px] border-[#E8E8E8]">
      <div className="pb-[1.6rem] border-b-[1px] border-b-[#E8E8E8]">
        <span className="text-[1.2rem] text-[#222] font-[500] leading-[1.4rem]">
          {branch.address}
        </span>
        <div className="flex items-center gap-[.4rem]">
          <span className="text-[2rem] text-[#000] leading-[3rem]">
            {branch.name}
          </span>
          <div
            className={`${
              branch.status == "Good"
                ? "bg-[#E0FBEC]"
                : branch.status === "Critical"
                ? "bg-[#FBE0E0]"
                : "bg-[#FAF4D2]"
            } py-[.2rem] px-[.9rem] rounded-[1.2rem] text-center`}
          >
            <span
              className={`${
                branch.status == "Good"
                  ? "text-[#117C43]"
                  : branch.status === "Critical"
                  ? "text-[#7C1111]"
                  : "text-[#534706]"
              } text-[1.2rem] tracking-[0.02rem]`}
            >
              {branch.status}
            </span>
          </div>
        </div>
      </div>
      <div className="pb-[1.2rem] border-b-[1px] border-b-[#E8E8E8]">
        <div className="">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-[.8rem]">
              <img src="/images/fraud-risk-icon.png" alt="" />
              <span className="text-[1.2rem] text-[#222] font-[500] leading-[2.4rem]">
                Fraud Risk Rate
              </span>
            </div>
            <span>{`${branch.fraud_risk}%`}</span>
          </div>
        </div>

        <div className="h-[.4rem] w-full rounded-[9rem] bg-[#E6E6E6] overflow-hidden">
          <div
            className="h-full"
            style={{
              backgroundImage: `linear-gradient(to right, #1CAF61 ${branch.fraud_risk}%, transparent ${branch.fraud_risk}%)`,
            }}
          ></div>
        </div>
      </div>
      <div className="flex justify-between gap-[.8rem] pb-[1.2rem] border-b-[1px] border-b-[#E8E8E8]">
        <div className="flex-1">
          <div className="flex items-center gap-[.4rem] ">
            <img src="/images/alerts-icon.png" alt="" />
            <span className="text-[1.2rem] text-[#222] font-[500] leading-[2.4rem]">
              Alerts
            </span>
          </div>
          <span className="text-[1.6rem] text-[#282828] font-[600] leading-[2.8rem]">
            {branch.alerts}
          </span>
        </div>
        <div className="flex-1">
          <div className=" flex items-center gap-[.4rem] ">
            <img src="/images/revenue-arrow.png" alt="" />
            <span className="text-[1.2rem] text-[#222] font-[500] leading-[2.4rem]">
              Revenue
            </span>
          </div>
          <span className="text-[1.6rem] text-[#282828] font-[600] leading-[2.8rem]">
            {`$${branch.revenue}`}
          </span>
        </div>
      </div>

      <div className="flex items-center justify-between">
        <span className="text-[1.1rem] text-[#8A8A8A] font-[500] leading-[2.8rem]">{`${branch.coordinates.latitude} / ${branch.coordinates.longitude}`}</span>
        <button className="cursor-pointer px-[1.35rem] text-[#fff] text-[1.2rem] font-[500] leading-[2.8rem]  rounded-[.6rem] border-[#0A77F5] shadow-[0px_2px_4px_0px_rgba(0, 0, 0, 0.05)] bg-[linear-gradient(180deg,#69ADF9_0%,#4D9DF8_100%)]">
          View Details
        </button>
      </div>
    </div>
  );
};

export default BranchCard;
