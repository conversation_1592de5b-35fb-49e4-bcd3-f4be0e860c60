import { createSlice } from "@reduxjs/toolkit";

interface IMiscState {
  activePath: string;
}

const initialState: IMiscState = {
  activePath: "",
};

const miscSlice = createSlice({
  name: "misc",
  initialState,
  reducers: {
    setActivePath: (state, action) => {
      state.activePath = action.payload;
    },
  },
});

export const { setActivePath } = miscSlice.actions;
export default miscSlice.reducer;
