import React from "react";
import Select from "react-select";
import type {
  GroupBase,
  OptionsOrGroups,
  DropdownIndicatorProps,
} from "react-select";

interface IDropdiwonProps {
  label: string;
  data: { label: string; value: string }[];
}

const CustomDropdownIndicator = (props: DropdownIndicatorProps) => {
  return (
    <div
      {...props.innerProps}
      className="react-select-dropdown-indicator-custom pl-[4px] pr-[2px] align-self-center"
    >
      <img src="/images/arrow-down.png" alt="" />
    </div>
  );
};

const Dropdown: React.FC<IDropdiwonProps> = ({ data, label }) => {
  return (
    <Select
      styles={{
        container: (baseStyle: Record<string, unknown>) => ({
          ...baseStyle,
          padding: "0px",
          display: "flex",
          alignItems: "center",
        }),
        valueContainer: (baseStyle: Record<string, unknown>) => ({
          ...baseStyle,
          padding: "0px",
          margin: "0",
        }),
        control: (baseStyle: Record<string, unknown>) => ({
          ...baseStyle,
          minHeight: "auto",
          cursor: "pointer",
          border: "#DFDFDF solid 1px",
          borderRadius: "6px",
          outline: "none",
          fontSize: "1.2rem",
          padding: "0px .75rem",
          paddintTop: "0",
          fontWeight: "500",
          color: "#333",
          boxShadow: "0px 2px 4px 0px rgba(0, 0, 0, 0.05)",
        }),
        indicatorsContainer: (provided: Record<string, unknown>) => ({
          ...provided,
          padding: "0",
        }),
        indicatorSeparator: (provided: Record<string, unknown>) => ({
          ...provided,
          display: "none",
        }),

        dropdownIndicator: (provided: Record<string, unknown>) => ({
          ...provided,
          padding: "0 1rem 2rems 0",
          color: "#7F7F7F",
        }),
      }}
      components={{
        DropdownIndicator: CustomDropdownIndicator as React.ComponentType<
          DropdownIndicatorProps<
            { label: string; value: string },
            boolean,
            GroupBase<{ label: string; value: string }>
          >
        >,
      }}
      placeholder={label || ""}
      options={
        data as OptionsOrGroups<
          { label: string; value: string },
          GroupBase<{ label: string; value: string }>
        >
      }
    />
  );
};

export default Dropdown;
