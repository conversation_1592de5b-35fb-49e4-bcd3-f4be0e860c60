import { createSlice } from "@reduxjs/toolkit";

const initialState: IDashboard = {
  dashboard: {
    overview: [
      {
        id: 1,
        iconPath: "/images/dashboard/check-circle.svg",
        iconBgColor: "#E0FBEC",
        title: "Overall Health Indicator",
        percentage: 96,
        trend: "",
        trendBgColor: "#E0FBEC",
        trendTextColor: "#117C43",
        trendText: "Good",
      },
      {
        id: 2,
        iconPath: "/images/dashboard/incognito.svg",
        iconBgColor: "#E0EDFB",
        title: "Fraud Risk",
        percentage: 2,
        trend: "up",
        trendBgColor: "#FBE0E0",
        trendTextColor: "#7C1111",
        trendText: "Increased 0.3%",
      },
      {
        id: 3,
        iconPath: "/images/dashboard/alert.svg",
        iconBgColor: "#E0EDFB",
        title: "Alerts summary",
        percentage: 23,
        trend: "down",
        trendBgColor: "#E0FBEC",
        trendTextColor: "#117C43",
        trendText: "Decreased 0.9%",
      },
    ],
    highRiskEmployees: {
      headers: [
        { key: "id", label: "ID" },
        { key: "employee", label: "Employee" },
        { key: "riskScore", label: "Risk Score", align: "center" as const },
        { key: "department", label: "Department" },
      ],
      data: [
        {
          id: "#2123",
          employee: "Giorgi Dzotsenidze",
          riskScore: 95,
          department: "Sales",
        },
        {
          id: "#2123",
          employee: "Giorgi Dzotsenidze",
          riskScore: 92,
          department: "Marketing",
        },
        {
          id: "#2123",
          employee: "Giorgi Dzotsenidze",
          riskScore: 84,
          department: "Operations",
        },
      ],
    },
    fraudAlerts: {
      headers: [
        { key: "time", label: "Time", align: "left" },
        { key: "status", label: "Status", align: "left" },
        { key: "employee", label: "Employee", align: "left" },
        { key: "branch", label: "Branch", align: "left" },
        { key: "type", label: "Type", align: "right" },
      ],
      data: [
        {
          id: 1,
          time: "12:00",
          status: "Open",
          employee: "Giorgi Dzotsenidze",
          branch: "Tbilisi, Dighoml",
          type: "Needs Attention",
        },
        {
          id: 2,
          time: "14:00",
          status: "In Review",
          employee: "Giorgi Dzotsenidze",
          branch: "Batumi",
          type: "Critical",
        },
        {
          id: 3,
          time: "14:00",
          status: "Resolved",
          employee: "Giorgi Dzotsenidze",
          branch: "Batumi",
          type: "Normal",
        },
        {
          id: 4,
          time: "14:00",
          status: "In Review",
          employee: "Giorgi Dzotsenidze",
          branch: "Batumi",
          type: "Critical",
        },
      ],
    },
    theftAlerts: {
      inHouse: [
        {
          title: "ქურდობა ვაკის ფილიალში",
          time: "12:00 - Jun 24 - 2023",
          score: 98,
          address: "Tbilisi, Vaza Phavela #13",
          text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry",
          avatarPath: "/images/dashboard/avatar.jpg",
          employee: "Giorgi Dzotsenidze",
        },
        {
          title: "ქურდობა ვაკის ფილიალში",
          time: "12:00 - Jun 24 - 2023",
          score: 98,
          address: "Tbilisi, Vaza Phavela #13",
          text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry",
          avatarPath: "/images/dashboard/avatar.jpg",
          employee: "Giorgi Dzotsenidze",
        },
        {
          title: "ქურდობა ვაკის ფილიალში",
          time: "12:00 - Jun 24 - 2023",
          score: 98,
          address: "Tbilisi, Vaza Phavela #13",
          text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry",
          avatarPath: "/images/dashboard/avatar.jpg",
          employee: "Giorgi Dzotsenidze",
        },
      ],
      customer: [
        {
          title: "ქურდობა ვაკის ფილიალში",
          time: "12:00 - Jun 24 - 2023",
          score: 98,
          address: "Tbilisi, Vaza Phavela #13",
          text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry",
          avatarPath: "/images/dashboard/avatar.jpg",
          employee: "Giorgi Dzotsenidze",
        },
        {
          title: "ქურდობა ვაკის ფილიალში",
          time: "12:00 - Jun 24 - 2023",
          score: 98,
          address: "Tbilisi, Vaza Phavela #13",
          text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry",
          avatarPath: null,
          employee: "Unknown Customer",
        },
        {
          title: "ქურდობა ვაკის ფილიალში",
          time: "12:00 - Jun 24 - 2023",
          score: 98,
          address: "Tbilisi, Vaza Phavela #13",
          text: "Lorem Ipsum is simply dummy text of the printing and typesetting industry",
          avatarPath: null,
          employee: "Unknown Customer",
        },
      ],
    },
  },
};

const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    setDashboard: (state, action) => {
      state.dashboard = action.payload;
    },
  },
});

export const { setDashboard } = dashboardSlice.actions;
export default dashboardSlice.reducer;
