import React from "react";
import { useNavigate } from "react-router-dom";
import { setActivePath } from "../../redux/misc/miscSlice";
import { useDispatch, useSelector } from "react-redux";
import type { RootState } from "../../redux/store";
import iconMap from "../../helpers/Icons";

interface INavButtonProps {
  name: string;
  icon: React.FC<IIconProps>;
  path: string;
  active: boolean;
  isOpen: boolean;
  showBlueDot: boolean;
  submenu?: { name: string; path: string; icon: string }[];
  isSubmenuOpen?: boolean;
  onToggleSubmenu?: () => void;
  onCloseAllSubmenus?: () => void;
}

const NavButton: React.FC<INavButtonProps> = ({
  name,
  icon,
  path,
  active,
  isOpen,
  showBlueDot = true,
  submenu,
  isSubmenuOpen = false,
  onToggleSubmenu,
  onCloseAllSubmenus,
}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { activePath } = useSelector((state: RootState) => state.misc);

  return (
    <>
      <li
        className="flex  items-center  cursor-pointer "
        onClick={() => {
          // Always set active path and navigate
          const fullPath = path === "" ? "/dashboard" : `/dashboard/${path}`;
          dispatch(setActivePath(fullPath));
          navigate(fullPath);

          // If has submenu, toggle it
          if (submenu && submenu.length > 0) {
            onToggleSubmenu?.();
          } else {
            // If no submenu, close all open submenus
            onCloseAllSubmenus?.();
          }
        }}
      >
        {showBlueDot ? (
          <div
            className={`w-[0.4rem] h-[1.2rem] rounded-tr-[.2rem] mr-[.8rem] rounded-br-[.2rem] hover:bg-[#4C9DF9] ${
              active ? "bg-[#4C9DF9]" : ""
            } `}
          ></div>
        ) : null}

        <div
          className={`flex items-center gap-[.8rem] hover:bg-[#F7F7F7] rounded-[.4rem] ${
            active ? "bg-[#F7F7F7]" : ""
          } p-[0.8rem] w-full`}
        >
          {React.createElement(icon, { active })}
          {isOpen ? (
            <span className="text-[1.2rem] text-[#2F2F2F]">{name}</span>
          ) : null}
          {submenu && submenu.length > 0 && isOpen ? (
            <div className="ml-auto">
              <img
                src="/images/chevron-right.png"
                alt=""
                className={`w-[1.2rem] h-[1.2rem] transition-transform duration-200 ${
                  isSubmenuOpen ? "rotate-90" : ""
                }`}
              />
            </div>
          ) : null}
        </div>
      </li>
      {submenu && submenu.length > 0 && isOpen && isSubmenuOpen ? (
        <ul className="ml-[2rem] space-y-[0.2rem]">
          {submenu.map((item, index) => {
            const isSubmenuActive = activePath === item.path;
            const SubmenuIcon = iconMap[item.icon];

            return (
              <li
                key={index}
                className="flex items-center cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation();
                  dispatch(setActivePath(item.path));
                  navigate(item.path);
                }}
              >
                <div
                  className={`w-[0.4rem] h-[1.2rem] rounded-tr-[.2rem] mr-[.8rem] rounded-br-[.2rem] hover:bg-[#4C9DF9] ${
                    isSubmenuActive ? "bg-[#4C9DF9]" : ""
                  }`}
                ></div>
                <div
                  className={`flex items-center gap-[.8rem] hover:bg-[#F7F7F7] rounded-[.4rem] ${
                    isSubmenuActive ? "bg-[#F7F7F7]" : ""
                  } p-[0.8rem] w-full`}
                >
                  {React.createElement(SubmenuIcon, {
                    active: isSubmenuActive,
                  })}
                  <span className="text-[1.2rem] text-[#2F2F2F]">
                    {item.name}
                  </span>
                </div>
              </li>
            );
          })}
        </ul>
      ) : null}
    </>
  );
};

export default NavButton;
