import React from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import type { RootState } from "../../redux/store";

import TableHeader from "../Shared/TableHeader";
import CommonTable from "../Shared/CommonTable";

const FraudAlerts: React.FC = () => {
  const navigate = useNavigate();

  const { dashboard } = useSelector((state: RootState) => state.dashboard);
  const { fraudAlerts } = dashboard;
  const { headers, data } = fraudAlerts;

  const redirectToFraudAlert = (id: number) => {
    navigate(`/fraud-alerts/${id}`);
  };

  return (
    <section className="mt-[2rem] border-1 border-solid border-[#E8E8E8] rounded-[1.6rem] bg-white overflow-hidden">
      <div className="w-full">
        <TableHeader
          title="Fraud Alerts"
          path="/fraud-alerts"
          icon="/images/dashboard/alert-black.svg"
        />
        <CommonTable
          headers={headers}
          data={data}
          redirect={true}
          className="w-full"
          redirectFunc={redirectToFraudAlert}
        />
      </div>
    </section>
  );
};

export default FraudAlerts;
