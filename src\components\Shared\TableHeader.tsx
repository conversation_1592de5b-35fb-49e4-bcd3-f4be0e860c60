import React from "react";
import { Link } from "react-router-dom";

interface TableHeaderProps {
  title: string;
  path?: string;
  icon: string;
  className?: string;
}

const TableHeader: React.FC<TableHeaderProps> = ({
  title,
  icon,
  path,
  className,
}) => {
  return (
    <div
      className={`w-full flex justify-between items-center p-[1.6rem] bg-white border-b-1 border-solid border-[#E8E8E8] ${className}`}
    >
      <div className="flex items-center gap-[0.8rem]">
        <img src={icon} alt="table-header-icon" />
        <p className="text-[1.4rem] leading-[2.8rem] font-[500] text-[#282828]">
          {title}
        </p>
      </div>
      {path ? (
        <Link to={path}>
          <button className="h-[2.8rem] px-[1rem] text-[1.4rem] leading-[1.6rem] font-medium text-[#333] border-1 border-solid border-[#DFDFDF] rounded-[0.6rem] cursor-pointer">
            View All
          </button>
        </Link>
      ) : null}
    </div>
  );
};

export default TableHeader;
