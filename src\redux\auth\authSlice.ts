import { createSlice } from "@reduxjs/toolkit";

interface IAuthState {
  user: User;
}

const initialState: IAuthState = {
  user: {
    first_name: "<PERSON><PERSON>",
    last_name: "<PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "************",
    role: "Business Owner",
  },
};

const authSlice = createSlice({
  name: "misc",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
    },
  },
});

export const { setUser } = authSlice.actions;
export default authSlice.reducer;
