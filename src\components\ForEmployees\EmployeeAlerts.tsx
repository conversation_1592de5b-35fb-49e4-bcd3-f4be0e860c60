import React from "react";
import AlertsTable from "../ForAlerts/AlertsTable";
import Pagination from "../Shared/Pagination";
import { useSelector } from "react-redux";
import type { RootState } from "../../redux/store";

const EmployeeAlerts: React.FC = () => {
  const { alerts } = useSelector((state: RootState) => state.alerts);
  return (
    <div className="pr-[2rem] h-full pb-[1rem]  overflow-y-auto">
      <div
        style={{ maxHeight: "calc(100vh - 20.5rem)" }}
        className="w-full overflow-y-auto "
      >
        <AlertsTable showUser={false} alerts={alerts} />
      </div>
      <Pagination
        pagination={{
          links: {
            next: "dfsgufdas",
            previous: null,
          },
          count: 10,
          total_pages: 10,
          current_page: 1,
        }}
        setCurrentPage={() => {}}
      />
    </div>
  );
};

export default EmployeeAlerts;
