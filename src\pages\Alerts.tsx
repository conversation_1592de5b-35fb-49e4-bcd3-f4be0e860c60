import React from "react";
import { Outlet, useLocation } from "react-router-dom";
import AlertsTable from "../components/ForAlerts/AlertsTable";
import { useSelector } from "react-redux";
import type { RootState } from "../redux/store";
import Pagination from "../components/Shared/Pagination";
const Alerts: React.FC = () => {
  const { alerts } = useSelector((state: RootState) => state.alerts);
  const location = useLocation();

  // Check if we're on a child route
  const isChildRoute = location.pathname !== "/dashboard/alerts";

  if (isChildRoute) {
    // Render child routes
    return <Outlet />;
  }

  return (
    <section className="pr-[2rem] pb-[1rem]  overflow-y-auto">
      <div
        style={{ height: "calc(100vh - 14.5rem)" }}
        className="max-h-[calc(100vh - 60rem)] overflow-y-auto "
      >
        <AlertsTable showUser={true} alerts={alerts} />
      </div>
      <Pagination
        pagination={{
          links: {
            next: "dfsgufdas",
            previous: null,
          },
          count: 10,
          total_pages: 10,
          current_page: 1,
        }}
        setCurrentPage={() => {}}
      />
    </section>
  );
};

export default Alerts;
