import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { useSelector } from "react-redux";
import type { RootState } from "../../redux/store";
import CurvedProgressBar from "./CurvedProgressBar";
import { singleEmployeeMenu } from "../../helpers/StaticData";
import SingleEmployeeTimeline from "./SingleEmployeeTimeline";
import EmployeeAlerts from "./EmployeeAlerts";

const SingleEmployee: React.FC = () => {
  const { id } = useParams();
  const { employees } = useSelector((state: RootState) => state.employees);
  const [employee, setEmployee] = useState<IEmployee>({
    id: 0,
    employee_id: "",
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    risk_score: 0,
    department: "",
    role: "",
    last_alert: "",
    branch: {
      name: "",
      address: "",
      phone: "",
      email: "",
      status: "",
      coordinates: { longitude: "", latitude: "" },
      fraud_risk: 0,
      alerts: 0,
      revenue: 0,
    },
    image: "",
  });
  const [currentMenuItemId, setCurrentMenuItemId] = useState(
    singleEmployeeMenu[0].id
  );

  useEffect(() => {
    setEmployee(
      employees.find((employee) => employee.id === Number(id)) as IEmployee
    );
  }, []);

  return (
    <div className="flex gap-[2rem]">
      <div className="flex flex-col gap-[2rem]">
        <div className="bg-[#fff] p-[2.4rem] border-[1px] border-[#E8E8E8] rounded-[1.6rem] w-[25.5rem]">
          <div className="flex flex-col items-center  border-b-[1px] border-b-[#E8E8E8] pb-[1.2rem]">
            <img
              src="/images/employee-image-large.png"
              className="w-[8.8rem]"
              alt=""
            />
            <span className="text-[1.6rem] font-[600] leading-[2.8rem]">
              {employee.first_name} {employee.last_name}
            </span>
            <span>ID:{employee.employee_id}</span>
          </div>
          <div className="flex flex-col items-center pt-[1.6rem]">
            <span className="text-[1.4rem] font-[600] leading-[2.8rem]">{`${employee.department}, ${employee.role}`}</span>
            <span className="text-[1.2rem] text-[#525252]">
              Joined 2 years ago
            </span>
            <button className="mt-[1.6rem] flex items-center justify-center gap-[.8rem] w-[11.5rem] py-[.55rem] text-1.2rem] text-[#333]  bg-[#fff] shadow-[0px_2px_4px_0px_rgba(0, 0, 0, 0.05)] border-[1px] border-[#DFDFDF] rounded-[.6rem]">
              Download CV <img src="/images/download-icon.png" alt="" />
            </button>
          </div>
        </div>
        <div className="text-center bg-[#fff] px-[2.4rem] pt-[2.4rem] border-[1px] border-[#E8E8E8] rounded-[1.6rem] w-[25.5rem]">
          <span className=" text-[1.4rem] font-[600] leading-[2.8rem]">
            Risk Score
          </span>
          <CurvedProgressBar />
        </div>
      </div>

      <div className={`${currentMenuItemId !== 1 ? "w-full" : ""}`}>
        <div className="w-[fit-content]">
          <ul className="list-none flex border-b-[1px] border-b-[#CECECE]">
            {singleEmployeeMenu.map((item) => (
              <li
                key={item.id}
                className={`relative ${
                  currentMenuItemId === item.id
                    ? "before:content-[''] before:absolute before:left-0 before:bottom-0 before:w-full before:h-[1px] before:bg-[#0A77F5]"
                    : "hover:before:content-[''] hover:before:absolute before:left-0 before:bottom-0 before:w-full before:h-[1px] before:bg-[#0A77F5]"
                } p-[1rem] cursor-pointer`}
                onClick={() => setCurrentMenuItemId(item.id)}
              >
                <span className="text-[1.2rem] font-[500] text-[#333]">
                  {item.name}
                </span>
              </li>
            ))}
          </ul>
        </div>
        <div className={`pt-[2rem] h-full`}>
          {currentMenuItemId === 1 && <SingleEmployeeTimeline />}
          {currentMenuItemId === 3 && <EmployeeAlerts />}
        </div>
      </div>
    </div>
  );
};
export default SingleEmployee;
