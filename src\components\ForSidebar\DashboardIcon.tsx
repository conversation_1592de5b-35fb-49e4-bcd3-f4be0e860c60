import React from "react";

interface IIconProps {
  active: boolean;
}

const DashboardIcon: React.FC<IIconProps> = ({ active }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={`${active ? "#4C9DF9" : "none"}`}
    >
      <path
        d="M4.46582 9.00488H4.53418C4.96348 9.00487 5.31375 9.00524 5.60254 9.02441L5.87207 9.05078C6.16993 9.09088 6.3897 9.16598 6.5625 9.30664L6.63379 9.37109C6.77101 9.50834 6.85651 9.67693 6.91016 9.89648L6.9541 10.1328C6.9989 10.466 7.00002 10.8983 7 11.4707V11.5391L6.99414 12.2979C6.99122 12.4083 6.98686 12.5113 6.98047 12.6074L6.9541 12.876C6.91402 13.1741 6.83899 13.3945 6.69824 13.5674L6.63379 13.6387C6.49656 13.7759 6.32789 13.8614 6.1084 13.915L5.87207 13.959C5.53889 14.0038 5.10661 14.0049 4.53418 14.0049H4.46582L3.70703 13.999C3.59661 13.9961 3.49363 13.9917 3.39746 13.9854L3.12793 13.959C2.83014 13.9189 2.61028 13.8437 2.4375 13.7031L2.36621 13.6387C2.22901 13.5015 2.14351 13.3327 2.08984 13.1133L2.0459 12.876C2.00114 12.5428 1.99998 12.1113 2 11.5391V11.4707L2.00586 10.7119C2.00878 10.6015 2.01315 10.4985 2.01953 10.4023L2.0459 10.1328C2.08598 9.83496 2.16117 9.61519 2.30176 9.44238L2.36621 9.37109C2.50348 9.23382 2.67199 9.14741 2.8916 9.09375L3.12793 9.05078C3.29445 9.02839 3.48604 9.01659 3.70703 9.01074L4.46582 9.00488ZM11.4658 9.00488H11.5342C11.9635 9.00487 12.3137 9.00524 12.6025 9.02441L12.8721 9.05078C13.1699 9.09088 13.3897 9.16598 13.5625 9.30664L13.6338 9.37109C13.771 9.50834 13.8565 9.67693 13.9102 9.89648L13.9541 10.1328C13.9989 10.466 14 10.8983 14 11.4707V11.5391L13.9941 12.2979C13.9912 12.4083 13.9869 12.5113 13.9805 12.6074L13.9541 12.876C13.9083 13.2167 13.8167 13.4557 13.6338 13.6387C13.4966 13.7759 13.3279 13.8614 13.1084 13.915L12.8721 13.959C12.5389 14.0038 12.1066 14.0049 11.5342 14.0049H11.4658L10.707 13.999C10.5966 13.9961 10.4936 13.9917 10.3975 13.9854L10.1279 13.959C9.83014 13.9189 9.61028 13.8437 9.4375 13.7031L9.36621 13.6387C9.22901 13.5015 9.14351 13.3327 9.08984 13.1133L9.0459 12.876C9.00114 12.5428 8.99998 12.1113 9 11.5391V11.4707L9.00586 10.7119C9.00878 10.6015 9.01315 10.4985 9.01953 10.4023L9.0459 10.1328C9.08598 9.83496 9.16117 9.61519 9.30176 9.44238L9.36621 9.37109C9.50348 9.23382 9.67199 9.14741 9.8916 9.09375L10.1279 9.05078C10.2944 9.02839 10.486 9.01659 10.707 9.01074L11.4658 9.00488ZM4.46582 2H4.53418C4.96346 1.99998 5.31376 2.00036 5.60254 2.01953L5.87207 2.0459C6.1699 2.08599 6.38971 2.16114 6.5625 2.30176L6.63379 2.36621C6.77102 2.50344 6.85649 2.67208 6.91016 2.8916L6.9541 3.12793C6.9989 3.46111 7.00002 3.89339 7 4.46582V4.53418L6.99414 5.29297C6.99122 5.4034 6.98685 5.50637 6.98047 5.60254L6.9541 5.87207C6.91401 6.1699 6.83886 6.38971 6.69824 6.5625L6.63379 6.63379C6.49656 6.77102 6.32792 6.85649 6.1084 6.91016L5.87207 6.9541C5.53889 6.9989 5.10661 7.00002 4.53418 7H4.46582L3.70703 6.99414C3.5966 6.99122 3.49363 6.98685 3.39746 6.98047L3.12793 6.9541C2.8301 6.91401 2.61029 6.83886 2.4375 6.69824L2.36621 6.63379C2.22898 6.49656 2.14351 6.32792 2.08984 6.1084L2.0459 5.87207C2.0011 5.53889 1.99998 5.1066 2 4.53418V4.46582L2.00586 3.70703C2.00878 3.5966 2.01315 3.49363 2.01953 3.39746L2.0459 3.12793C2.08599 2.8301 2.16114 2.61029 2.30176 2.4375L2.36621 2.36621C2.50344 2.22898 2.67208 2.14351 2.8916 2.08984L3.12793 2.0459C3.29444 2.02351 3.48604 2.01171 3.70703 2.00586L4.46582 2ZM11.4658 2H11.5342C11.9635 1.99998 12.3138 2.00036 12.6025 2.01953L12.8721 2.0459C13.1699 2.08599 13.3897 2.16114 13.5625 2.30176L13.6338 2.36621C13.771 2.50344 13.8565 2.67208 13.9102 2.8916L13.9541 3.12793C13.9989 3.46111 14 3.89339 14 4.46582V4.53418L13.9941 5.29297C13.9912 5.4034 13.9869 5.50637 13.9805 5.60254L13.9541 5.87207C13.914 6.1699 13.8389 6.38971 13.6982 6.5625L13.6338 6.63379C13.4966 6.77102 13.3279 6.85649 13.1084 6.91016L12.8721 6.9541C12.5389 6.9989 12.1066 7.00002 11.5342 7H11.4658L10.707 6.99414C10.5966 6.99122 10.4936 6.98685 10.3975 6.98047L10.1279 6.9541C9.8301 6.91401 9.61029 6.83886 9.4375 6.69824L9.36621 6.63379C9.22898 6.49656 9.14351 6.32792 9.08984 6.1084L9.0459 5.87207C9.0011 5.53889 8.99998 5.10661 9 4.53418V4.46582L9.00586 3.70703C9.00878 3.5966 9.01315 3.49363 9.01953 3.39746L9.0459 3.12793C9.08599 2.8301 9.16114 2.61029 9.30176 2.4375L9.36621 2.36621C9.50344 2.22898 9.67208 2.14351 9.8916 2.08984L10.1279 2.0459C10.2944 2.02351 10.486 2.01171 10.707 2.00586L11.4658 2Z"
        stroke={active ? "#4C9DF9" : `#55555E`}
      />
    </svg>
  );
};

export default DashboardIcon;
