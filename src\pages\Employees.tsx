import React from "react";
import EmployeesTable from "../components/ForEmployees/EmployeesTable";
import { useSelector } from "react-redux";
import type { RootState } from "../redux/store";
import Pagination from "../components/Shared/Pagination";

const Employees: React.FC = () => {
  const { employees } = useSelector((state: RootState) => state.employees);

  return (
    <section className="pr-[2rem] pb-[1rem]  overflow-y-auto">
      <div
        style={{ height: "calc(100vh - 14.5rem)" }}
        className="max-h-[calc(100vh - 60rem)] overflow-y-auto "
      >
        <EmployeesTable employees={employees} />
      </div>
      <Pagination
        pagination={{
          links: {
            next: "dfsgufdas",
            previous: null,
          },
          count: 10,
          total_pages: 10,
          current_page: 1,
        }}
        setCurrentPage={() => {}}
      />
    </section>
  );
};

export default Employees;
