import React, { useState } from "react";
import { mainMenu, appMenu, additionalNav } from "../helpers/StaticData";
import NavButton from "./ForSidebar/NavButton";
import { useSelector } from "react-redux";
import type { RootState } from "../redux/store";
import iconMap from "../helpers/Icons";

const Sidebar: React.FC = () => {
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const [openAdditionalMenu, setOpenAdditionalMenu] = useState<boolean>(false);
  const [openSubmenus, setOpenSubmenus] = useState<{ [key: string]: boolean }>(
    {}
  );
  const { activePath } = useSelector((state: RootState) => state.misc);
  const { user } = useSelector((state: RootState) => state.auth);

  // Toggle submenu function - closes other submenus when opening a new one
  const toggleSubmenu = (menuName: string) => {
    setOpenSubmenus((prev) => {
      const isCurrentlyOpen = prev[menuName];

      // If clicking on the same menu that's already open, just close it
      if (isCurrentlyOpen) {
        return {
          ...prev,
          [menuName]: false,
        };
      }

      // If opening a new submenu, close all others and open this one
      const newState: { [key: string]: boolean } = {};
      Object.keys(prev).forEach((key) => {
        newState[key] = false;
      });
      newState[menuName] = true;

      return newState;
    });
  };

  // Close all submenus function
  const closeAllSubmenus = () => {
    setOpenSubmenus({});
  };

  return (
    <div
      className={`flex flex-col justify-between relative bg-[#fff] ${
        isOpen ? "w-[21rem]" : "w-[5.6rem]"
      } min-h-[calc(100vh-2.4rem)] py-[1.2rem] pr-[1.2rem] border-[1px] border-[#E8E8E8] background-[#FFF] rounded-[1.2rem] m-[1.2rem]`}
    >
      <div>
        <div className="pl-[1.2rem]">
          <div className=" flex justify-between items-center w-full pb-[1.6rem]   border-b-[1px] border-b-[#E8E8E8] mb-[1.2rem] ">
            <img
              src={`${
                isOpen ? "/images/logo.png" : "/images/logo-no-text.png"
              }`}
              alt=""
            />
            <img
              src="/images/open-menu.png"
              alt=""
              className={`cursor-pointer ${
                isOpen ? "" : "absolute right-[-1rem]"
              }`}
              onClick={() => setIsOpen(!isOpen)}
            />
          </div>
        </div>
        <div className="flex flex-col gap-[.6rem]">
          <h3 className="text-[1.2rem] leading-[1.6rem] text-[#666] ml-[1.2rem]">
            Main
          </h3>
          <ul className="list-none flex flex-col gap-[.4rem]">
            {mainMenu.map((item, index) => (
              <NavButton
                name={item.name}
                icon={iconMap[item.icon]}
                key={index}
                path={item.path}
                active={activePath === item.path}
                isOpen={isOpen}
                showBlueDot={true}
                submenu={item.submenu || []}
                isSubmenuOpen={openSubmenus[item.name] || false}
                onToggleSubmenu={() => toggleSubmenu(item.name)}
                onCloseAllSubmenus={closeAllSubmenus}
              />
            ))}
          </ul>
        </div>
      </div>

      <div className="">
        <div className="flex flex-col gap-[.6rem]">
          <h3 className="text-[1.2rem] leading-[1.6rem] text-[#666] ml-[1.2rem]">
            App
          </h3>
          <ul className="list-none flex flex-col gap-[.4rem]">
            {appMenu.map((item, index) => (
              <NavButton
                name={item.name}
                icon={iconMap[item.icon]}
                key={index}
                path={item.path}
                active={activePath === item.path}
                isOpen={isOpen}
                showBlueDot={true}
                isSubmenuOpen={openSubmenus[item.name] || false}
                onToggleSubmenu={() => toggleSubmenu(item.name)}
                onCloseAllSubmenus={closeAllSubmenus}
              />
            ))}
          </ul>
        </div>

        <div
          className="relative pl-[1.2rem] mt-[1.2rem] cursor-pointer"
          onClick={() => setOpenAdditionalMenu(!openAdditionalMenu)}
        >
          <div className="flex items-center justify-between border-t-[1px] border-t-[#E8E8E8] pt-[1.6rem]">
            <span className="text-[1.2rem] text-[#3F3F46] font-[600] leading-[2rem] bg-[#DFDFDF] rounded-[.6rem] p-[.4rem]">
              {`${user.first_name[0]}${user.last_name[0]}`}
            </span>
            {isOpen ? (
              <div className="flex flex-col">
                <span className="text-[1.4rem] text-[#3F3F46] font-[600] leading-[1.5rem]">{`${user.first_name} ${user.last_name}`}</span>
                <span className="text-[1.2rem] text-[#3F3F46] font-[400] leading-[1.6rem]">
                  {user.role}
                </span>
              </div>
            ) : null}

            <img src="/images/right-arrow.png" alt="" />
            {openAdditionalMenu ? (
              <div
                className={`absolute flex flex-col w-[20.2rem] p-[.8rem] rounded-[1.2rem] border-[1px] border-[#E8E8E8] bg-[#fff] ${
                  isOpen ? "left-[110%]" : "left-[145%]"
                } bottom-[-5%]`}
              >
                <ul className="list-none flex flex-col gap-[.4rem]">
                  {additionalNav.map((item, index) => (
                    <NavButton
                      name={item.name}
                      icon={iconMap[item.icon]}
                      key={index}
                      path={item.path}
                      active={activePath === item.path}
                      isOpen={true}
                      showBlueDot={false}
                      isSubmenuOpen={openSubmenus[item.name] || false}
                      onToggleSubmenu={() => toggleSubmenu(item.name)}
                      onCloseAllSubmenus={closeAllSubmenus}
                    />
                  ))}
                </ul>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
