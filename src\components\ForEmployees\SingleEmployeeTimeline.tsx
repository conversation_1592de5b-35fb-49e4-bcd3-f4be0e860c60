import React from "react";

const SingleEmployeeTimeline: React.FC = () => {
  return (
    <div>
      <ul className="list-none flex flex-col gap-[2rem]">
        <li className="w-full flex items-center gap-[1.6rem]">
          <div className="p-[.8rem] bg-[#fff] rounded-[.6rem]">
            <img src="/images/dollar.png" alt="" />
          </div>
          <div className="flex flex-col flex-1 mr-[1.6rem]">
            <span className="text-[1.4rem] text-[#333] font-[600] ">
              Transaction Processed
            </span>
            <span className="text-[1.2rem] font-[500] text-[#333]">
              $500 transaction processed
            </span>
          </div>
          <span className="text-[1.2rem] text-left font-[500] text-[#333] min-w-[12rem]">
            15:30 - Today
          </span>
        </li>
        <li className="w-full flex items-center gap-[1.6rem]">
          <div className="p-[.8rem] bg-[#fff] rounded-[.6rem]">
            <img src="/images/clock.png" alt="" />
          </div>
          <div className="flex flex-col flex-1">
            <span className="text-[1.4rem] text-[#333] font-[600] ">
              Login Activity
            </span>
            <span className="text-[1.2rem] font-[500] text-[#333]">
              Logged in
            </span>
          </div>
          <span className="text-[1.2rem] font-[500] text-[#333] min-w-[12rem]">
            09:00 - Today
          </span>
        </li>
        <li className="w-full flex items-center gap-[1.6rem]">
          <div className="p-[.8rem] bg-[#fff] rounded-[.6rem]">
            <img src="/images/database.png" alt="" />
          </div>
          <div className="flex flex-col flex-1">
            <span className="text-[1.4rem] text-[#333] font-[600] ">
              Data Access
            </span>
            <span className="text-[1.2rem] font-[500] text-[#333]">
              Accessed financial data
            </span>
          </div>
          <span className="text-[1.2rem] font-[500] text-[#333] min-w-[12rem]">
            17:00 - 24 Jun - 2025
          </span>
        </li>
        <li className="w-full flex items-center gap-[1.6rem]">
          <div className="p-[.8rem] bg-[#fff] rounded-[.6rem]">
            <img src="/images/file.png" alt="" />
          </div>
          <div className="flex flex-col flex-1">
            <span className="text-[1.4rem] text-[#333] font-[600] ">
              Report Generated
            </span>
            <span className="text-[1.2rem] font-[500] text-[#333]">
              Generated monthly report
            </span>
          </div>
          <span className="text-[1.2rem] font-[500] text-[#333] min-w-[12rem]">
            16:00 - 24 Jun - 2025
          </span>
        </li>
      </ul>
    </div>
  );
};

export default SingleEmployeeTimeline;
