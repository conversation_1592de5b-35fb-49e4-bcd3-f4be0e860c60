import React from "react";
import { Link } from "react-router";
interface IEmployeeTableProps {
  employees: IEmployee[];
}

const EmployeesTable: React.FC<IEmployeeTableProps> = ({ employees }) => {
  return (
    <div className="bg-[#fff] rounded-[1.6rem] border-[1px] border-[#E8E8E8] pb-[1rem]">
      <div className="flex items-center gap-[.8rem] p-[1.6rem]">
        <img src="/images/employee-table-img.png" alt="" />
        <span className="text-[1.4rem] font-[500] leading-[2.8rem] text-[#282828]">
          Employees
        </span>
      </div>
      <div className="">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="!bg-[#fff] border-t-[1px] border-t-[#E8E8E8]">
            <tr className="">
              <th
                scope="col"
                className=" sticky top-0 backdrop-blur-sm backdrop-filter  py-3.5 pr-3 pl-[1.6rem] text-left text-[1.2rem] font-[500] text-[#282828] "
              >
                ID
              </th>
              <th
                scope="col"
                className="hidden sticky top-0 backdrop-blur-sm backdrop-filter px-3 py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] lg:table-cell"
              >
                Employee
              </th>
              <th
                scope="col"
                className="hidden sticky top-0 backdrop-blur-sm backdrop-filter px-3 py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Branch
              </th>
              <th
                scope="col"
                className="hidden sticky top-0 backdrop-blur-sm backdrop-filter px-3 py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Departament
              </th>
              <th
                scope="col"
                className="px-3 sticky top-0 backdrop-blur-sm backdrop-filter py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Role
              </th>
              <th
                scope="col"
                className="px-3 sticky top-0 backdrop-blur-sm backdrop-filter py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Last Alert
              </th>
              <th
                scope="col"
                className="px-3 sticky top-0 backdrop-blur-sm backdrop-filter py-3.5 text-left text-[1.2rem] font-[500] text-[#282828] sm:table-cell"
              >
                Risk Score
              </th>
              <th scope="col" className="relative py-3.5 pr-4 pl-3 sm:pr-0">
                <span className="sr-only">Edit</span>
              </th>
            </tr>
          </thead>
          <tbody className=" divide-y divide-gray-200 bg-white">
            {employees.map((employee, index) => (
              <tr key={index}>
                <td className="hidden !pr-[0] pl-[1.6rem] py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                  {employee.employee_id}
                </td>
                <td className="w-full max-w-0 py-4 pr-3 text-[1.2rem] pl-[.75rem] font-[500] text-[#000] sm:w-auto sm:max-w-none ">
                  <div className="flex items-center gap-[.8rem]">
                    <img src="/images/employee-image.png" alt="" />
                    {`${employee.first_name} ${employee.last_name}`}
                  </div>
                  <dl className="font-normal lg:hidden">
                    <dt className="sr-only">Title</dt>
                    <dd className="mt-1 truncate text-gray-700">
                      Front-end Developer
                    </dd>
                    <dt className="sr-only sm:hidden">Email</dt>
                    <dd className="mt-1 truncate text-gray-500 sm:hidden">
                      <EMAIL>
                    </dd>
                  </dl>
                </td>
                <td className="hidden px-3 py-4 text-[1.2rem] font-[500] text-[#000] lg:table-cell">
                  {employee.branch.name}
                </td>
                <td className="hidden px-3 py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                  {employee.department}
                </td>
                <td className="px-3 py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                  {employee.role}
                </td>
                <td className="px-3 py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                  {employee.last_alert}
                </td>
                <td className="px-3 py-4 text-[1.2rem] font-[500] text-[#000] sm:table-cell">
                  {employee.risk_score}
                </td>
                <td className="py-4 pr-4 pl-3 text-right text-sm font-medium sm:pr-0">
                  <Link
                    to={`${employee.id}`}
                    className="text-indigo-600 hover:text-indigo-900"
                  >
                    <img src="/images/goto-employee.png" alt="" />
                  </Link>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default EmployeesTable;
