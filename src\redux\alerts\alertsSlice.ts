import { createSlice } from "@reduxjs/toolkit";

interface IAlertsState {
  alerts: IAlert[];
}

const initialState: IAlertsState = {
  alerts: [
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "<PERSON><PERSON><PERSON>",
        last_name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    {
      id: 1,
      time: "12:00",
      employee: {
        id: 1,
        employee_id: "#123456",
        first_name: "Giorgi",
        last_name: "Dzotsenidze",
        email: "<EMAIL>",
        phone: "************",
        risk_score: 3,
        department: "sales",
        role: "Assistant",
        last_alert: "2025-6-15",
        branch: {
          name: "Vake #3",
          address: "Tbilisi, Chavchavadze #12",
          phone: "************",
          email: "<EMAIL>",
          status: "Good",
          coordinates: { longitude: "44.787197", latitude: "41.715137" },
          fraud_risk: 3,
          alerts: 12,
          revenue: 15010,
        },
        image: "/images/employee-image.png",
      },
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        phone: "************",
        email: "<EMAIL>",
        status: "Good",
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        fraud_risk: 3,
        alerts: 12,
        revenue: 15010,
      },

      status: "Open",
      type: "Normal",
      message: "N.J deleted something in Branch1 sales",
    },
    // {
    //   id: 1,
    //   time: "12:00",
    //   employee: {
    //     id: 1,
    //     employee_id: "#123456",
    //     first_name: "Giorgi",
    //     last_name: "Dzotsenidze",
    //     email: "<EMAIL>",
    //     phone: "************",
    //     risk_score: 3,
    //     department: "sales",
    //     role: "Assistant",
    //     last_alert: "2025-6-15",
    //     branch: {
    //       name: "Vake #3",
    //       address: "Tbilisi, Chavchavadze #12",
    //       phone: "************",
    //       email: "<EMAIL>",
    //       status: "Good",
    //       coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //       fraud_risk: 3,
    //       alerts: 12,
    //       revenue: 15010,
    //     },
    //     image: "/images/employee-image.png",
    //   },
    //   branch: {
    //     name: "Vake #3",
    //     address: "Tbilisi, Chavchavadze #12",
    //     phone: "************",
    //     email: "<EMAIL>",
    //     status: "Good",
    //     coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //     fraud_risk: 3,
    //     alerts: 12,
    //     revenue: 15010,
    //   },

    //   status: "Open",
    //   type: "Normal",
    //   message: "N.J deleted something in Branch1 sales",
    // },
    // {
    //   id: 1,
    //   time: "12:00",
    //   employee: {
    //     id: 1,
    //     employee_id: "#123456",
    //     first_name: "Giorgi",
    //     last_name: "Dzotsenidze",
    //     email: "<EMAIL>",
    //     phone: "************",
    //     risk_score: 3,
    //     department: "sales",
    //     role: "Assistant",
    //     last_alert: "2025-6-15",
    //     branch: {
    //       name: "Vake #3",
    //       address: "Tbilisi, Chavchavadze #12",
    //       phone: "************",
    //       email: "<EMAIL>",
    //       status: "Good",
    //       coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //       fraud_risk: 3,
    //       alerts: 12,
    //       revenue: 15010,
    //     },
    //     image: "/images/employee-image.png",
    //   },
    //   branch: {
    //     name: "Vake #3",
    //     address: "Tbilisi, Chavchavadze #12",
    //     phone: "************",
    //     email: "<EMAIL>",
    //     status: "Good",
    //     coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //     fraud_risk: 3,
    //     alerts: 12,
    //     revenue: 15010,
    //   },

    //   status: "Open",
    //   type: "Normal",
    //   message: "N.J deleted something in Branch1 sales",
    // },
    // {
    //   id: 1,
    //   time: "12:00",
    //   employee: {
    //     id: 1,
    //     employee_id: "#123456",
    //     first_name: "Giorgi",
    //     last_name: "Dzotsenidze",
    //     email: "<EMAIL>",
    //     phone: "************",
    //     risk_score: 3,
    //     department: "sales",
    //     role: "Assistant",
    //     last_alert: "2025-6-15",
    //     branch: {
    //       name: "Vake #3",
    //       address: "Tbilisi, Chavchavadze #12",
    //       phone: "************",
    //       email: "<EMAIL>",
    //       status: "Good",
    //       coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //       fraud_risk: 3,
    //       alerts: 12,
    //       revenue: 15010,
    //     },
    //     image: "/images/employee-image.png",
    //   },
    //   branch: {
    //     name: "Vake #3",
    //     address: "Tbilisi, Chavchavadze #12",
    //     phone: "************",
    //     email: "<EMAIL>",
    //     status: "Good",
    //     coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //     fraud_risk: 3,
    //     alerts: 12,
    //     revenue: 15010,
    //   },

    //   status: "Open",
    //   type: "Normal",
    //   message: "N.J deleted something in Branch1 sales",
    // },
    // {
    //   id: 1,
    //   time: "12:00",
    //   employee: {
    //     id: 1,
    //     employee_id: "#123456",
    //     first_name: "Giorgi",
    //     last_name: "Dzotsenidze",
    //     email: "<EMAIL>",
    //     phone: "************",
    //     risk_score: 3,
    //     department: "sales",
    //     role: "Assistant",
    //     last_alert: "2025-6-15",
    //     branch: {
    //       name: "Vake #3",
    //       address: "Tbilisi, Chavchavadze #12",
    //       phone: "************",
    //       email: "<EMAIL>",
    //       status: "Good",
    //       coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //       fraud_risk: 3,
    //       alerts: 12,
    //       revenue: 15010,
    //     },
    //     image: "/images/employee-image.png",
    //   },
    //   branch: {
    //     name: "Vake #3",
    //     address: "Tbilisi, Chavchavadze #12",
    //     phone: "************",
    //     email: "<EMAIL>",
    //     status: "Good",
    //     coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //     fraud_risk: 3,
    //     alerts: 12,
    //     revenue: 15010,
    //   },

    //   status: "Open",
    //   type: "Normal",
    //   message: "N.J deleted something in Branch1 sales",
    // },
    // {
    //   id: 1,
    //   time: "12:00",
    //   employee: {
    //     id: 1,
    //     employee_id: "#123456",
    //     first_name: "Giorgi",
    //     last_name: "Dzotsenidze",
    //     email: "<EMAIL>",
    //     phone: "************",
    //     risk_score: 3,
    //     department: "sales",
    //     role: "Assistant",
    //     last_alert: "2025-6-15",
    //     branch: {
    //       name: "Vake #3",
    //       address: "Tbilisi, Chavchavadze #12",
    //       phone: "************",
    //       email: "<EMAIL>",
    //       status: "Good",
    //       coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //       fraud_risk: 3,
    //       alerts: 12,
    //       revenue: 15010,
    //     },
    //     image: "/images/employee-image.png",
    //   },
    //   branch: {
    //     name: "Vake #3",
    //     address: "Tbilisi, Chavchavadze #12",
    //     phone: "************",
    //     email: "<EMAIL>",
    //     status: "Good",
    //     coordinates: { longitude: "44.787197", latitude: "41.715137" },
    //     fraud_risk: 3,
    //     alerts: 12,
    //     revenue: 15010,
    //   },

    //   status: "Open",
    //   type: "Normal",
    //   message: "N.J deleted something in Branch1 sales",
    // },
  ],
};

const alertsSlice = createSlice({
  name: "misc",
  initialState,
  reducers: {
    setAlerts: (state, action) => {
      state.alerts = action.payload;
    },
  },
});

export const { setAlerts } = alertsSlice.actions;
export default alertsSlice.reducer;
