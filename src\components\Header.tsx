import React from "react";
import Dropdown from "./Shared/Dropdown";
import { useSelector } from "react-redux";
import { useLocation, useParams } from "react-router";
import type { RootState } from "../redux/store";
import { dropDowns } from "../helpers/StaticData";
import { useNavigate } from "react-router";

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { activePath } = useSelector((state: RootState) => state.misc);
  const location = useLocation();
  const { id } = useParams();

  // Check if we're on a single employee page
  const isSingleEmployeePage =
    location.pathname.includes("/dashboard/employees/") && id;

  // Get employee data if on single employee page
  // Use the appropriate options based on the page
  const options = dropDowns[activePath];

  return (
    <header
      className={`w-full h-[fit-content] flex justify-between py-[2.6rem] px-[2rem] ${
        activePath !== "branches" ? "pr-[2rem] pl-[1rem]" : ""
      }`}
    >
      <div className="flex items-center gap-[1.2rem]">
        {isSingleEmployeePage && (
          <div
            className="flex items-center gap-[.6rem] cursor-pointer"
            onClick={() => navigate("/dashboard/employees")}
          >
            <img src="/images/arrow-left.png" alt="" />
            <span className="text-[1.2rem] font-[500] text-[#333]">
              Go Back
            </span>
          </div>
        )}

        {!isSingleEmployeePage && options
          ? Object.keys(options).map((item, index) => (
              <Dropdown
                key={index}
                data={options[item].options}
                label={options[item].label}
              />
            ))
          : null}
      </div>

      <div className="flex items-center gap-[.8rem]">
        <div className="flex gap-[1rem] py-[0.3rem] pr-[.6rem] pl-[1.2rem] border-[1px] border-[#E4E4E7] rounded-[.8rem] bg-[#F8F8F8]">
          <input
            type="text"
            className=" w-[20rem] outline-none text-[1.4rem]"
            placeholder="Search..."
          />
          <div className="flex items-center gap-[.4rem]">
            <span className="text-[1.4rem] text-[#71717A] font-[500] leading-[2rem]">
              Press
            </span>
            <div className="flex items-center justify-center gap-[.2rem] bg-[#fff] border-[1px] border-[#E4E4E7] rounded-[.4rem] p-[.4rem]">
              <img src="/images/command.png" alt="" />
              <img src="/images/slash.png" alt="" />
            </div>
          </div>
        </div>
        <div className="relative cursor-pointer bg-[#fff] rounded-[.8rem] p-[.8rem]">
          <img src="/images/bell.png" alt="" />
          <div className="absolute right-0 top-0 w-[.6rem] h-[.6rem] bg-[#0A77F5] rounded-full"></div>
        </div>
      </div>
    </header>
  );
};

export default Header;
