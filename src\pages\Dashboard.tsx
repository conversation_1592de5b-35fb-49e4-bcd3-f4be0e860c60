import React from "react";

import Overview from "../components/dashboard/Overview";
import HighRiskEmployees from "../components/dashboard/HighRiskEmployees";
import FraudAlerts from "../components/dashboard/FraudAlerts";
import TheftAlerts from "../components/dashboard/TheftAlerts";
import TableHeader from "../components/Shared/TableHeader";

const Dashboard: React.FC = () => {
  return (
    <>
      <section className="flex border-1 border-solid border-[#E8E8E8] rounded-[1.6rem] bg-white overflow-hidden">
        <Overview />
        <HighRiskEmployees />
      </section>

      <FraudAlerts />

      <TheftAlerts>
        <TableHeader
          title="Theft Alerts"
          icon="/images/dashboard/alert-black.svg"
          className="rounded-[1.6rem]"
          path="/dashboard/alerts"
        />
      </TheftAlerts>
    </>
  );
};

export default Dashboard;
