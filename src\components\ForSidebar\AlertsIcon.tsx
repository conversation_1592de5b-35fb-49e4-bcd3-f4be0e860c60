import React from "react";

interface IIconProps {
  active: boolean;
}

const AlertsIcon: React.FC<IIconProps> = ({ active }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill={`${active ? "#4C9DF9" : "none"}`}
    >
      <path
        d="M8 5.66675V9.33341"
        stroke={active ? "#4C9DF9" : `#55555E`}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 11.4976V11.1643"
        stroke={active ? "#4C9DF9" : `#55555E`}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.98703 7.17433C5.73804 3.72478 6.61355 2 8 2C9.38645 2 10.262 3.72477 12.013 7.17432L12.2312 7.60418C13.6863 10.4707 14.4138 11.904 13.7562 12.952C13.0987 14 11.4719 14 8.2182 14H7.7818C4.52814 14 2.9013 14 2.24375 12.952C1.58621 11.904 2.31375 10.4707 3.76883 7.60418L3.98703 7.17433Z"
        stroke={active ? "#4C9DF9" : `#55555E`}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default AlertsIcon;
